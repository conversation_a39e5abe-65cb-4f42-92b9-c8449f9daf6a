// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in ubuzima_app/test/appointment_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:ubuzima_app/core/models/appointment.dart' as _i2;
import 'package:ubuzima_app/core/models/time_slot.dart' as _i3;
import 'package:ubuzima_app/core/services/appointment_service.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAppointment_0 extends _i1.SmartFake implements _i2.Appointment {
  _FakeAppointment_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTimeSlot_1 extends _i1.SmartFake implements _i3.TimeSlot {
  _FakeTimeSlot_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AppointmentService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppointmentService extends _i1.Mock
    implements _i4.AppointmentService {
  MockAppointmentService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i2.Appointment>> getAppointments({
    String? status,
    String? date,
    int? page = 0,
    int? size = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAppointments, [], {
              #status: status,
              #date: date,
              #page: page,
              #size: size,
            }),
            returnValue: _i5.Future<List<_i2.Appointment>>.value(
              <_i2.Appointment>[],
            ),
          )
          as _i5.Future<List<_i2.Appointment>>);

  @override
  _i5.Future<List<_i2.Appointment>> getHealthWorkerAppointments(
    int? healthWorkerId, {
    String? status,
    String? date,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getHealthWorkerAppointments,
              [healthWorkerId],
              {#status: status, #date: date},
            ),
            returnValue: _i5.Future<List<_i2.Appointment>>.value(
              <_i2.Appointment>[],
            ),
          )
          as _i5.Future<List<_i2.Appointment>>);

  @override
  _i5.Future<_i2.Appointment> createAppointment({
    required int? healthFacilityId,
    int? healthWorkerId,
    required String? appointmentType,
    required DateTime? scheduledDate,
    int? durationMinutes,
    String? reason,
    String? notes,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createAppointment, [], {
              #healthFacilityId: healthFacilityId,
              #healthWorkerId: healthWorkerId,
              #appointmentType: appointmentType,
              #scheduledDate: scheduledDate,
              #durationMinutes: durationMinutes,
              #reason: reason,
              #notes: notes,
            }),
            returnValue: _i5.Future<_i2.Appointment>.value(
              _FakeAppointment_0(
                this,
                Invocation.method(#createAppointment, [], {
                  #healthFacilityId: healthFacilityId,
                  #healthWorkerId: healthWorkerId,
                  #appointmentType: appointmentType,
                  #scheduledDate: scheduledDate,
                  #durationMinutes: durationMinutes,
                  #reason: reason,
                  #notes: notes,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Appointment>);

  @override
  _i5.Future<_i2.Appointment> updateAppointment(
    int? appointmentId, {
    String? appointmentType,
    DateTime? scheduledDate,
    int? durationMinutes,
    String? reason,
    String? notes,
    String? status,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #updateAppointment,
              [appointmentId],
              {
                #appointmentType: appointmentType,
                #scheduledDate: scheduledDate,
                #durationMinutes: durationMinutes,
                #reason: reason,
                #notes: notes,
                #status: status,
              },
            ),
            returnValue: _i5.Future<_i2.Appointment>.value(
              _FakeAppointment_0(
                this,
                Invocation.method(
                  #updateAppointment,
                  [appointmentId],
                  {
                    #appointmentType: appointmentType,
                    #scheduledDate: scheduledDate,
                    #durationMinutes: durationMinutes,
                    #reason: reason,
                    #notes: notes,
                    #status: status,
                  },
                ),
              ),
            ),
          )
          as _i5.Future<_i2.Appointment>);

  @override
  _i5.Future<bool> updateAppointmentStatus(
    int? appointmentId,
    String? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateAppointmentStatus, [
              appointmentId,
              status,
            ]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> cancelAppointment(int? appointmentId, String? reason) =>
      (super.noSuchMethod(
            Invocation.method(#cancelAppointment, [appointmentId, reason]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> deleteAppointment(int? appointmentId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAppointment, [appointmentId]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<_i2.Appointment> rescheduleAppointment(
    int? appointmentId,
    DateTime? newScheduledDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#rescheduleAppointment, [
              appointmentId,
              newScheduledDate,
            ]),
            returnValue: _i5.Future<_i2.Appointment>.value(
              _FakeAppointment_0(
                this,
                Invocation.method(#rescheduleAppointment, [
                  appointmentId,
                  newScheduledDate,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Appointment>);

  @override
  _i5.Future<List<_i3.TimeSlot>> getTimeSlots({
    int? healthWorkerId,
    int? healthFacilityId,
    String? date,
    bool? isAvailable,
    int? page = 0,
    int? size = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getTimeSlots, [], {
              #healthWorkerId: healthWorkerId,
              #healthFacilityId: healthFacilityId,
              #date: date,
              #isAvailable: isAvailable,
              #page: page,
              #size: size,
            }),
            returnValue: _i5.Future<List<_i3.TimeSlot>>.value(<_i3.TimeSlot>[]),
          )
          as _i5.Future<List<_i3.TimeSlot>>);

  @override
  _i5.Future<List<_i3.TimeSlot>> getAvailableTimeSlots({
    required int? healthFacilityId,
    int? healthWorkerId,
    required String? date,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableTimeSlots, [], {
              #healthFacilityId: healthFacilityId,
              #healthWorkerId: healthWorkerId,
              #date: date,
            }),
            returnValue: _i5.Future<List<_i3.TimeSlot>>.value(<_i3.TimeSlot>[]),
          )
          as _i5.Future<List<_i3.TimeSlot>>);

  @override
  _i5.Future<_i3.TimeSlot> createTimeSlot({
    required int? healthFacilityId,
    required int? healthWorkerId,
    required DateTime? startTime,
    required DateTime? endTime,
    bool? isAvailable = true,
    String? reason,
    int? maxAppointments = 1,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createTimeSlot, [], {
              #healthFacilityId: healthFacilityId,
              #healthWorkerId: healthWorkerId,
              #startTime: startTime,
              #endTime: endTime,
              #isAvailable: isAvailable,
              #reason: reason,
              #maxAppointments: maxAppointments,
            }),
            returnValue: _i5.Future<_i3.TimeSlot>.value(
              _FakeTimeSlot_1(
                this,
                Invocation.method(#createTimeSlot, [], {
                  #healthFacilityId: healthFacilityId,
                  #healthWorkerId: healthWorkerId,
                  #startTime: startTime,
                  #endTime: endTime,
                  #isAvailable: isAvailable,
                  #reason: reason,
                  #maxAppointments: maxAppointments,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.TimeSlot>);

  @override
  _i5.Future<_i3.TimeSlot> updateTimeSlot(
    int? timeSlotId, {
    DateTime? startTime,
    DateTime? endTime,
    bool? isAvailable,
    String? reason,
    int? maxAppointments,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #updateTimeSlot,
              [timeSlotId],
              {
                #startTime: startTime,
                #endTime: endTime,
                #isAvailable: isAvailable,
                #reason: reason,
                #maxAppointments: maxAppointments,
              },
            ),
            returnValue: _i5.Future<_i3.TimeSlot>.value(
              _FakeTimeSlot_1(
                this,
                Invocation.method(
                  #updateTimeSlot,
                  [timeSlotId],
                  {
                    #startTime: startTime,
                    #endTime: endTime,
                    #isAvailable: isAvailable,
                    #reason: reason,
                    #maxAppointments: maxAppointments,
                  },
                ),
              ),
            ),
          )
          as _i5.Future<_i3.TimeSlot>);

  @override
  _i5.Future<bool> deleteTimeSlot(int? timeSlotId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTimeSlot, [timeSlotId]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);
}
