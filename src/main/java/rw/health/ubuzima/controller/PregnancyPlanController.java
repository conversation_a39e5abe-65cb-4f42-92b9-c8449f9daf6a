package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.PregnancyPlan;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.PregnancyPlanStatus;
import rw.health.ubuzima.repository.PregnancyPlanRepository;
import rw.health.ubuzima.repository.UserRepository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/pregnancy-plans")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class PregnancyPlanController {

    private final PregnancyPlanRepository pregnancyPlanRepository;
    private final UserRepository userRepository;

    // GET all plans for a user with optional status
    @GetMapping
    public ResponseEntity<Map<String, Object>> getPregnancyPlans(
            @RequestParam Long userId,
            @RequestParam(required = false) String status) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of("success", false, "message", "User not found"));
            }

            List<PregnancyPlan> plans;
            if (status != null) {
                PregnancyPlanStatus planStatus = PregnancyPlanStatus.valueOf(status.toUpperCase());
                plans = pregnancyPlanRepository.findByUserAndCurrentStatus(user, planStatus);
            } else {
                plans = pregnancyPlanRepository.findByUserIdOrderByCreatedAtDesc(userId);
            }

            return ResponseEntity.ok(Map.of("success", true, "plans", plans));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "message", "Failed to fetch pregnancy plans: " + e.getMessage()));
        }
    }
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> createPregnancyPlan(@RequestBody Map<String, Object> request) {
        System.out.println("🚀 CREATE PREGNANCY PLAN ENDPOINT HIT");
        System.out.println("📦 Request Data: " + request);

        try {
            // Log each step to see where it fails
            System.out.println("📋 Step 1: Validating userId...");
            if (!request.containsKey("userId") || request.get("userId") == null) {
                System.out.println("❌ userId is missing or null");
                return ResponseEntity.badRequest().body(Map.of("success", false, "message", "userId is required"));
            }

            System.out.println("📋 Step 2: Converting userId to Long...");
            Long userId = Long.valueOf(request.get("userId").toString());
            System.out.println("✅ UserId converted: " + userId);

            System.out.println("📋 Step 3: Finding user in database...");
            User user = userRepository.findById(userId).orElse(null);

            if (user == null) {
                System.out.println("❌ User not found with ID: " + userId);
                return ResponseEntity.badRequest().body(Map.of("success", false, "message", "User not found"));
            }
            System.out.println("✅ User found: " + user.getName());

            System.out.println("📋 Step 4: Validating planName...");
            if (!request.containsKey("planName") || request.get("planName") == null ||
                    request.get("planName").toString().trim().isEmpty()) {
                System.out.println("❌ planName is missing or empty");
                return ResponseEntity.badRequest().body(Map.of("success", false, "message", "planName is required"));
            }

            System.out.println("📋 Step 5: Creating pregnancy plan object...");
            PregnancyPlan plan = new PregnancyPlan();
            plan.setUser(user);
            plan.setPlanName(request.get("planName").toString().trim());
            System.out.println("✅ Basic plan created with name: " + plan.getPlanName());

            // Handle optional fields with try-catch
            System.out.println("📋 Step 6: Processing optional fields...");

            try {
                if (request.containsKey("partnerId") && request.get("partnerId") != null) {
                    Long partnerId = Long.valueOf(request.get("partnerId").toString());
                    User partner = userRepository.findById(partnerId).orElse(null);
                    plan.setPartner(partner);
                    System.out.println("✅ Partner set: " + (partner != null ? partner.getName() : "null"));
                }
            } catch (Exception e) {
                System.out.println("⚠️ Partner setting failed: " + e.getMessage());
            }

            try {
                if (request.containsKey("targetConceptionDate") && request.get("targetConceptionDate") != null) {
                    String dateString = request.get("targetConceptionDate").toString();
                    System.out.println("📅 Original date string: " + dateString);

                    // Handle ISO format
                    if (dateString.contains("T")) {
                        dateString = dateString.split("T")[0];
                        System.out.println("📅 Cleaned date string: " + dateString);
                    }

                    LocalDate date = LocalDate.parse(dateString);
                    plan.setTargetConceptionDate(date);
                    System.out.println("✅ Date parsed successfully: " + date);
                }
            } catch (Exception e) {
                System.out.println("❌ Date parsing failed: " + e.getMessage());
                return ResponseEntity.badRequest().body(Map.of("success", false,
                        "message", "Invalid date format: " + e.getMessage()));
            }

            // Set other optional fields safely
            if (request.containsKey("preconceptionGoals") && request.get("preconceptionGoals") != null) {
                plan.setPreconceptionGoals(request.get("preconceptionGoals").toString());
            }

            if (request.containsKey("healthPreparations") && request.get("healthPreparations") != null) {
                plan.setHealthPreparations(request.get("healthPreparations").toString());
            }

            if (request.containsKey("lifestyleChanges") && request.get("lifestyleChanges") != null) {
                plan.setLifestyleChanges(request.get("lifestyleChanges").toString());
            }

            if (request.containsKey("medicalConsultations") && request.get("medicalConsultations") != null) {
                plan.setMedicalConsultations(request.get("medicalConsultations").toString());
            }

            // Set default status
            try {
                if (request.containsKey("currentStatus") && request.get("currentStatus") != null) {
                    plan.setCurrentStatus(PregnancyPlanStatus.valueOf(request.get("currentStatus").toString().toUpperCase()));
                } else {
                    plan.setCurrentStatus(PregnancyPlanStatus.PLANNING);
                }
            } catch (Exception e) {
                System.out.println("⚠️ Status setting failed, using default: " + e.getMessage());
                plan.setCurrentStatus(PregnancyPlanStatus.PLANNING);
            }

            System.out.println("📋 Step 7: Saving to database...");
            PregnancyPlan savedPlan = pregnancyPlanRepository.save(plan);
            System.out.println("✅ Plan saved successfully with ID: " + savedPlan.getId());

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Pregnancy plan created successfully",
                    "data", savedPlan  // Changed from "plan" to "data" to match your ApiResponse expectation
            ));

        } catch (Exception e) {
            System.out.println("🔴 FULL ERROR DETAILS:");
            System.out.println("🔴 Error message: " + e.getMessage());
            System.out.println("🔴 Error class: " + e.getClass().getName());
            e.printStackTrace(); // This will show the full stack trace

            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "message", "Failed to create pregnancy plan: " + e.getMessage()));
        }
    }

    // PUT update pregnancy plan
    @PutMapping("/{planId}")
    public ResponseEntity<Map<String, Object>> updatePregnancyPlan(
            @PathVariable Long planId,
            @RequestBody Map<String, Object> request) {
        try {
            PregnancyPlan plan = pregnancyPlanRepository.findById(planId).orElse(null);
            if (plan == null) return ResponseEntity.notFound().build();

            if (request.get("planName") != null) plan.setPlanName(request.get("planName").toString());
            if (request.get("targetConceptionDate") != null)
                plan.setTargetConceptionDate(LocalDate.parse(request.get("targetConceptionDate").toString()));
            if (request.get("currentStatus") != null)
                plan.setCurrentStatus(PregnancyPlanStatus.valueOf(request.get("currentStatus").toString().toUpperCase()));
            if (request.get("preconceptionGoals") != null) plan.setPreconceptionGoals(request.get("preconceptionGoals").toString());
            if (request.get("healthPreparations") != null) plan.setHealthPreparations(request.get("healthPreparations").toString());
            if (request.get("lifestyleChanges") != null) plan.setLifestyleChanges(request.get("lifestyleChanges").toString());
            if (request.get("medicalConsultations") != null) plan.setMedicalConsultations(request.get("medicalConsultations").toString());
            if (request.get("progressNotes") != null) plan.setProgressNotes(request.get("progressNotes").toString());

            PregnancyPlan updatedPlan = pregnancyPlanRepository.save(plan);

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Pregnancy plan updated successfully",
                    "plan", updatedPlan
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "message", "Failed to update pregnancy plan: " + e.getMessage()));
        }
    }

    // DELETE pregnancy plan
    @DeleteMapping("/{planId}")
    public ResponseEntity<Map<String, Object>> deletePregnancyPlan(
            @PathVariable Long planId,
            @RequestParam Long userId) {
        try {
            PregnancyPlan plan = pregnancyPlanRepository.findById(planId).orElse(null);
            if (plan == null) return ResponseEntity.notFound().build();

            if (!plan.getUser().getId().equals(userId)) {
                return ResponseEntity.badRequest()
                        .body(Map.of("success", false, "message", "You can only delete your own pregnancy plans"));
            }

            pregnancyPlanRepository.delete(plan);

            return ResponseEntity.ok(Map.of("success", true, "message", "Pregnancy plan deleted successfully"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "message", "Failed to delete pregnancy plan: " + e.getMessage()));
        }
    }

    // GET shared plans
    @GetMapping("/shared")
    public ResponseEntity<Map<String, Object>> getSharedPlans(@RequestParam Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) return ResponseEntity.badRequest()
                    .body(Map.of("success", false, "message", "User not found"));

            List<PregnancyPlan> plans = pregnancyPlanRepository.findByUserOrPartner(user);

            return ResponseEntity.ok(Map.of("success", true, "plans", plans));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "message", "Failed to fetch shared plans: " + e.getMessage()));
        }
    }

    // GET active plans
    @GetMapping("/active")
    public ResponseEntity<Map<String, Object>> getActivePlans() {
        try {
            List<PregnancyPlan> activePlans = pregnancyPlanRepository.findActivePlans();
            return ResponseEntity.ok(Map.of("success", true, "plans", activePlans));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "message", "Failed to fetch active plans: " + e.getMessage()));
        }
    }

    // POST update plan status
    @PostMapping("/{planId}/status")
    public ResponseEntity<Map<String, Object>> updatePlanStatus(
            @PathVariable Long planId,
            @RequestParam String status,
            @RequestParam Long userId) {
        try {
            PregnancyPlan plan = pregnancyPlanRepository.findById(planId).orElse(null);
            if (plan == null) return ResponseEntity.notFound().build();

            if (!plan.getUser().getId().equals(userId) &&
                    (plan.getPartner() == null || !plan.getPartner().getId().equals(userId))) {
                return ResponseEntity.badRequest()
                        .body(Map.of("success", false, "message", "You can only update plans you own or are partnered with"));
            }

            plan.setCurrentStatus(PregnancyPlanStatus.valueOf(status.toUpperCase()));
            PregnancyPlan updatedPlan = pregnancyPlanRepository.save(plan);

            return ResponseEntity.ok(Map.of("success", true, "message", "Plan status updated successfully", "plan", updatedPlan));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "message", "Failed to update plan status: " + e.getMessage()));
        }
    }
}
