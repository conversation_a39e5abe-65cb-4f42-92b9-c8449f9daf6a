package rw.health.ubuzima.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import rw.health.ubuzima.util.JwtUtil;

import java.io.IOException;
import java.util.List;

@Component
@RequiredArgsConstructor
public class JwtAuthFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hai<PERSON> filterChain)
            throws ServletException, IOException {

        String path = request.getRequestURI();
        logger.info("➡️ Incoming request path: " + path);

        // Skip JWT check for /ping test
        if (path.equals("/api/v1/test/ping")) {
            logger.info("ℹ️ Skipping JWT check for /ping endpoint");
            filterChain.doFilter(request, response);
            return;
        }

        final String authHeader = request.getHeader("Authorization");
        logger.info("🔹 Authorization header: " + authHeader);

        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            logger.warn("⚠️ Missing or invalid Authorization header");
            filterChain.doFilter(request, response);
            return;
        }

        try {
            // Extract token
            String token = authHeader.substring(7);
            logger.info("🔑 Extracted JWT token: " + token);

            // Validate token and get claims
            String username = jwtUtil.extractUsername(token);
            String role = jwtUtil.extractRole(token);
            Long userId = jwtUtil.extractUserId(token);

            logger.info("🔹 JWT claims -> username: " + username + ", role: " + role + ", userId: " + userId);

            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                if (!jwtUtil.isTokenExpired(token)) {
                    // Create authentication token with user details and role
                    var authorities = List.of(new SimpleGrantedAuthority("ROLE_" + role));
                    var authToken = new UsernamePasswordAuthenticationToken(
                            username,
                            null,
                            authorities
                    );

                    // Set authentication in context
                    SecurityContextHolder.getContext().setAuthentication(authToken);
                    logger.info("✅ User authenticated successfully with role: " + role);
                } else {
                    logger.warn("⚠️ JWT token is expired for user: " + username);
                }
            }

        } catch (Exception e) {
            logger.error("❌ Error validating JWT token", e);
        }

        filterChain.doFilter(request, response);
    }
}
