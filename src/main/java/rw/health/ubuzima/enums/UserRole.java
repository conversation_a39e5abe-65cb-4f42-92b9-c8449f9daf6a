package rw.health.ubuzima.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum UserRole {
    CLIENT("client"),
    HEALTH_WORKER("healthWorker"),
    ADMIN("admin");

    private final String value;

    UserRole(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    public static UserRole fromValue(String value) {
        for (UserRole role : UserRole.values()) {
            if (role.value.equals(value)) {
                return role;
            }
        }
        throw new IllegalArgumentException("Unknown role: " + value);
    }
}
