# Alternative properties file (application.yml takes precedence)
# This file is kept for reference and backup configuration

# Server Configuration
server.address=0.0.0.0
server.port=8080
server.servlet.context-path=/api/v1

# Database Configuration
spring.datasource.url=*******************************************
spring.datasource.username=postgres
spring.datasource.password=hirwa
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Logging Configuration
logging.level.rw.health.ubuzima=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# Management Endpoints
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# JWT Configuration
ubuzima.jwt.secret=ubuzima-secret-key-2024-very-long-secret-key-for-jwt-token-generation
ubuzima.jwt.expiration=86400000
ubuzima.jwt.refresh-expiration=604800000

server.error.include-message=always
server.error.include-binding-errors=always
