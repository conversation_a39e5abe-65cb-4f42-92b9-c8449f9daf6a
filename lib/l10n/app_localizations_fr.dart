// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'Ubuzima';

  @override
  String get appTagline => 'Autonomiser votre parcours de santé';

  @override
  String get appDescription =>
      'Votre compagnon complet pour la planification familiale, la santé reproductive et le bien-être - conçu avec soin pour la femme moderne';

  @override
  String get welcomeBack => 'Bon retour';

  @override
  String get signInToContinue =>
      'Connectez-vous pour continuer votre parcours de santé';

  @override
  String get email => 'E-mail';

  @override
  String get emailAddress => 'Adresse e-mail';

  @override
  String get enterEmail => 'Entrez votre e-mail';

  @override
  String get pleaseEnterEmail => 'Veuillez entrer votre e-mail';

  @override
  String get pleaseEnterValidEmail => 'Veuillez entrer un e-mail valide';

  @override
  String get password => 'Mot de passe';

  @override
  String get enterPassword => 'Entrez votre mot de passe';

  @override
  String get pleaseEnterPassword => 'Veuillez entrer votre mot de passe';

  @override
  String get passwordTooShort =>
      'Le mot de passe doit contenir au moins 6 caractères';

  @override
  String get rememberMe => 'Se souvenir de moi';

  @override
  String get signIn => 'Se connecter';

  @override
  String get forgotPassword => 'Mot de passe oublié ?';

  @override
  String get or => 'OU';

  @override
  String get dontHaveAccount => 'Vous n\'avez pas de compte ?';

  @override
  String get createAccount => 'Créer un compte';

  @override
  String get termsOfService => 'Conditions d\'utilisation';

  @override
  String get privacyPolicy => 'Politique de confidentialité';

  @override
  String get bySigningIn => 'En vous connectant, vous acceptez nos';

  @override
  String get and => ' et ';

  @override
  String get goodMorning => 'Bonjour';

  @override
  String get goodAfternoon => 'Bon après-midi';

  @override
  String get goodEvening => 'Bonsoir';

  @override
  String get administrator => 'Administrateur';

  @override
  String get healthWorker => 'Agent de santé';

  @override
  String get client => 'Client';

  @override
  String get loginSuccess => 'Connexion réussie !';

  @override
  String get registrationSuccess => 'Inscription réussie !';

  @override
  String get updateSuccess => 'Mis à jour avec succès !';

  @override
  String get deleteSuccess => 'Supprimé avec succès !';

  @override
  String get consultation => 'Consultation';

  @override
  String get familyPlanning => 'Planification familiale';

  @override
  String get prenatalCare => 'Soins prénatals';

  @override
  String get postnatalCare => 'Soins postnatals';

  @override
  String get vaccination => 'Vaccination';

  @override
  String get healthScreening => 'Dépistage de santé';

  @override
  String get followUp => 'Suivi';

  @override
  String get emergency => 'Urgence';

  @override
  String get counseling => 'Conseil';

  @override
  String get other => 'Autre';

  @override
  String get onceDaily => 'Une fois par jour';

  @override
  String get twiceDaily => 'Deux fois par jour';

  @override
  String get threeTimes => 'Trois fois par jour';

  @override
  String get fourTimes => 'Quatre fois par jour';

  @override
  String get asNeeded => 'Au besoin';

  @override
  String get weekly => 'Hebdomadaire';

  @override
  String get monthly => 'Mensuel';

  @override
  String get spotting => 'Spotting';

  @override
  String get light => 'Léger';

  @override
  String get medium => 'Moyen';

  @override
  String get heavy => 'Lourd';

  @override
  String get language => 'Langue';

  @override
  String get selectLanguage => 'Sélectionner la langue';

  @override
  String get english => 'English';

  @override
  String get french => 'Français';

  @override
  String get kinyarwanda => 'Kinyarwanda';

  @override
  String get welcomeBackComma => 'Bon retour,';

  @override
  String get letsTrackYourHealthJourney => 'Suivons votre parcours de santé';

  @override
  String get appointmentManagement => 'Gestion des rendez-vous';

  @override
  String get myAppointments => 'Mes rendez-vous';

  @override
  String get healthOverview => 'Aperçu de la santé';

  @override
  String get totalRecords => 'Total des dossiers';

  @override
  String get recentRecords => 'Récent (30j)';

  @override
  String get generalConsultation => 'Consultation générale';

  @override
  String get followUpVisit => 'Visite de suivi';

  @override
  String get healthCounseling => 'Conseil en santé';

  @override
  String get dashboard => 'Tableau de bord';

  @override
  String get appointments => 'Rendez-vous';

  @override
  String get healthRecords => 'Dossiers de santé';

  @override
  String get medications => 'Médicaments';

  @override
  String get education => 'Éducation';

  @override
  String get community => 'Communauté';

  @override
  String get profile => 'Profil';

  @override
  String get settings => 'Paramètres';

  @override
  String get logout => 'Déconnexion';

  @override
  String get home => 'Accueil';

  @override
  String get health => 'Santé';

  @override
  String get bookAppointment => 'Prendre rendez-vous';

  @override
  String get viewRecords => 'Voir les dossiers';

  @override
  String get trackCycle => 'Suivre le cycle';

  @override
  String get aiAssistant => 'Assistant IA';

  @override
  String get readScreenContentAloud =>
      'Lire le contenu de l\'écran à haute voix';

  @override
  String get today => 'Aujourd\'hui';

  @override
  String get upcoming => 'À venir';

  @override
  String get past => 'Passé';

  @override
  String get all => 'Tout';

  @override
  String get manageSlots => 'Gérer les créneaux';

  @override
  String get save => 'Enregistrer';

  @override
  String get cancel => 'Annuler';

  @override
  String get delete => 'Supprimer';

  @override
  String get edit => 'Modifier';

  @override
  String get add => 'Ajouter';

  @override
  String get search => 'Rechercher';

  @override
  String get filter => 'Filtrer';

  @override
  String get sort => 'Trier';

  @override
  String get refresh => 'Actualiser';

  @override
  String get loading => 'Chargement...';

  @override
  String get error => 'Erreur';

  @override
  String get success => 'Succès';

  @override
  String get warning => 'Avertissement';

  @override
  String get info => 'Information';

  @override
  String get confirm => 'Confirmer';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get ok => 'OK';

  @override
  String get close => 'Proche';

  @override
  String get back => 'Arrière';

  @override
  String get next => 'Suivant';

  @override
  String get previous => 'Précédent';

  @override
  String get done => 'Terminé';

  @override
  String get submit => 'Soumettre';

  @override
  String get send => 'Envoyer';

  @override
  String get receive => 'Recevoir';

  @override
  String get view => 'Vue';

  @override
  String get details => 'Détails';

  @override
  String get more => 'Plus';

  @override
  String get less => 'Moins';

  @override
  String get show => 'Spectacle';

  @override
  String get hide => 'Masquer';

  @override
  String get select => 'Sélectionner';

  @override
  String get choose => 'Choisir';

  @override
  String get pick => 'Choisir';

  @override
  String get upload => 'Télécharger';

  @override
  String get download => 'Télécharger';

  @override
  String get share => 'Partager';

  @override
  String get copy => 'Copier';

  @override
  String get paste => 'Coller';

  @override
  String get cut => 'Couper';

  @override
  String get undo => 'Annuler';

  @override
  String get redo => 'Refaire';

  @override
  String get clear => 'Clair';

  @override
  String get reset => 'Réinitialiser';

  @override
  String get update => 'Mise à jour';

  @override
  String get create => 'Créer';

  @override
  String get open => 'Ouvert';

  @override
  String get bookNewAppointment => 'Prendre un nouveau rendez-vous';

  @override
  String get appointmentBooked => 'Rendez-vous pris';

  @override
  String get appointmentCancelled => 'Rendez-vous annulé';

  @override
  String get appointmentConfirmed => 'Rendez-vous confirmé';

  @override
  String get appointmentRescheduled => 'Rendez-vous reprogrammé';

  @override
  String get noAppointments => 'Aucun rendez-vous';

  @override
  String get noAppointmentsFound => 'Aucun rendez-vous trouvé';

  @override
  String get appointmentDetails => 'Détails du rendez-vous';

  @override
  String get appointmentType => 'Type de rendez-vous';

  @override
  String get appointmentDate => 'Date du rendez-vous';

  @override
  String get appointmentTime => 'Heure du rendez-vous';

  @override
  String get appointmentLocation => 'Lieu du rendez-vous';

  @override
  String get appointmentNotes => 'Notes du rendez-vous';

  @override
  String get appointmentStatus => 'Statut du rendez-vous';

  @override
  String get appointmentDuration => 'Durée du rendez-vous';

  @override
  String get appointmentReminder => 'Rappel de rendez-vous';

  @override
  String get rescheduleAppointment => 'Reprogrammer le rendez-vous';

  @override
  String get cancelAppointment => 'Annuler le rendez-vous';

  @override
  String get confirmAppointment => 'Confirmer le rendez-vous';

  @override
  String get editAppointment => 'Modifier le rendez-vous';

  @override
  String get deleteAppointment => 'Supprimer le rendez-vous';

  @override
  String get viewAppointment => 'Voir le rendez-vous';

  @override
  String get searchAppointments => 'Rechercher des rendez-vous';

  @override
  String get filterAppointments => 'Filtrer les rendez-vous';

  @override
  String get sortAppointments => 'Trier les rendez-vous';

  @override
  String get exportAppointments => 'Exporter les rendez-vous';

  @override
  String get printAppointments => 'Imprimer les rendez-vous';

  @override
  String get shareAppointments => 'Partager les rendez-vous';

  @override
  String get syncAppointments => 'Synchroniser les rendez-vous';

  @override
  String get backupAppointments => 'Sauvegarder les rendez-vous';

  @override
  String get restoreAppointments => 'Restaurer les rendez-vous';

  @override
  String get importAppointments => 'Importer les rendez-vous';

  @override
  String get scheduled => 'Programmé';

  @override
  String get confirmed => 'Confirmé';

  @override
  String get completed => 'Terminé';

  @override
  String get cancelled => 'Annulé';

  @override
  String get rescheduled => 'Reprogrammé';

  @override
  String get pending => 'En attente';

  @override
  String get approved => 'Approuvé';

  @override
  String get rejected => 'Rejeté';

  @override
  String get inProgress => 'En cours';

  @override
  String get onHold => 'En attente';

  @override
  String get delayed => 'Retardé';

  @override
  String get urgent => 'Urgent';

  @override
  String get normal => 'Normal';

  @override
  String get low => 'Faible';

  @override
  String get high => 'Élevé';

  @override
  String get critical => 'Critique';

  @override
  String get routine => 'Routine';

  @override
  String get diagnosis => 'Diagnostic';

  @override
  String get treatment => 'Traitement';

  @override
  String get therapy => 'Thérapie';

  @override
  String get surgery => 'Chirurgie';

  @override
  String get procedure => 'Procédure';

  @override
  String get test => 'Test';

  @override
  String get examination => 'Examen';

  @override
  String get visit => 'Visite';

  @override
  String get session => 'Séance';

  @override
  String get assessment => 'Évaluation';

  @override
  String get evaluation => 'Évaluation';

  @override
  String get review => 'Révision';

  @override
  String get analysis => 'Analyse';

  @override
  String get report => 'Rapport';

  @override
  String get summary => 'Résumé';

  @override
  String get overview => 'Aperçu';

  @override
  String get history => 'Historique';

  @override
  String get record => 'Dossier';

  @override
  String get file => 'Fichier';

  @override
  String get document => 'Document';

  @override
  String get form => 'Formulaire';

  @override
  String get application => 'Application';

  @override
  String get request => 'Demande';

  @override
  String get order => 'Ordre';

  @override
  String get prescription => 'Ordonnance';

  @override
  String get medication => 'Médicament';

  @override
  String get medicine => 'Médicament';

  @override
  String get drug => 'Médicament';

  @override
  String get pill => 'Pilule';

  @override
  String get tablet => 'Comprimé';

  @override
  String get capsule => 'Capsule';

  @override
  String get injection => 'Injection';

  @override
  String get vaccine => 'Vaccin';

  @override
  String get shot => 'Injection';

  @override
  String get dose => 'Dose';

  @override
  String get dosage => 'Dosage';

  @override
  String get frequency => 'Fréquence';

  @override
  String get schedule => 'Horaire';

  @override
  String get duration => 'Durée';

  @override
  String get period => 'Période';

  @override
  String get interval => 'Intervalle';

  @override
  String get cycle => 'Cycle';

  @override
  String get phase => 'Phase';

  @override
  String get stage => 'Étape';

  @override
  String get step => 'Étape';

  @override
  String get level => 'Niveau';

  @override
  String get grade => 'Grade';

  @override
  String get degree => 'Degré';

  @override
  String get severity => 'Gravité';

  @override
  String get intensity => 'Intensité';

  @override
  String get strength => 'Force';

  @override
  String get wellness => 'Bien-être';

  @override
  String get condition => 'Condition';

  @override
  String get state => 'État';

  @override
  String get status => 'Statut';

  @override
  String get situation => 'Situation';

  @override
  String get position => 'Position';

  @override
  String get location => 'Emplacement';

  @override
  String get place => 'Lieu';

  @override
  String get area => 'Zone';

  @override
  String get region => 'Région';

  @override
  String get zone => 'Zone';

  @override
  String get center => 'Centre';

  @override
  String get clinic => 'Clinique';

  @override
  String get hospital => 'Hôpital';

  @override
  String get pharmacy => 'Pharmacie';

  @override
  String get laboratory => 'Laboratoire';

  @override
  String get office => 'Bureau';

  @override
  String get room => 'Salle';

  @override
  String get department => 'Département';

  @override
  String get unit => 'Unité';

  @override
  String get division => 'Division';

  @override
  String get section => 'Section';

  @override
  String get service => 'Service';

  @override
  String get program => 'Programme';

  @override
  String get project => 'Projet';

  @override
  String get plan => 'Plan';

  @override
  String get method => 'Méthode';

  @override
  String get technique => 'Technique';

  @override
  String get approach => 'Approche';

  @override
  String get system => 'Système';

  @override
  String get network => 'Réseau';

  @override
  String get platform => 'Plateforme';

  @override
  String get contraception => 'Contraception';

  @override
  String get contraceptiveMethods => 'Méthodes Contraceptives';

  @override
  String get myMethods => 'Mes Méthodes';

  @override
  String get sideEffects => 'Effets Secondaires';

  @override
  String get sideEffectsReports => 'Rapports d\'Effets Secondaires';

  @override
  String get reportSideEffect => 'Signaler un Effet Secondaire';

  @override
  String get addMethod => 'Ajouter une Méthode';

  @override
  String get editMethod => 'Modifier la Méthode';

  @override
  String get deleteMethod => 'Supprimer la Méthode';

  @override
  String get chooseMethod => 'Choisir une Méthode';

  @override
  String get methodName => 'Nom de la Méthode';

  @override
  String get methodDescription => 'Description de la Méthode';

  @override
  String get effectiveness => 'Efficacité';

  @override
  String get instructions => 'Instructions';

  @override
  String get startDate => 'Date de Début';

  @override
  String get endDate => 'Date de Fin';

  @override
  String get nextAppointment => 'Prochain Rendez-vous';

  @override
  String get prescribedBy => 'Prescrit par';

  @override
  String get isActive => 'Est Actif';

  @override
  String get additionalNotes => 'Notes Supplémentaires';

  @override
  String get birthControlPills => 'Pilules Contraceptives';

  @override
  String get implant => 'Implant';

  @override
  String get iud => 'DIU';

  @override
  String get condoms => 'Préservatifs';

  @override
  String get diaphragm => 'Diaphragme';

  @override
  String get patch => 'Patch';

  @override
  String get ring => 'Anneau';

  @override
  String get naturalFamilyPlanning => 'Planification Familiale Naturelle';

  @override
  String get sterilization => 'Stérilisation';

  @override
  String get emergencyContraception => 'Contraception d\'Urgence';

  @override
  String get hormonalMethods => 'Méthodes Hormonales';

  @override
  String get barrierMethods => 'Méthodes Barrières';

  @override
  String get naturalMethods => 'Méthodes Naturelles';

  @override
  String get permanentMethods => 'Méthodes Permanentes';

  @override
  String get otherMethods => 'Autres Méthodes';

  @override
  String get sideEffectName => 'Effet Secondaire';

  @override
  String get description => 'Description';

  @override
  String get dateReported => 'Date Signalée';

  @override
  String get retry => 'Réessayer';

  @override
  String get optional => 'Optionnel';

  @override
  String get pleaseSelectMethod =>
      'Veuillez sélectionner une méthode contraceptive';

  @override
  String get sideEffectReported => 'Effet secondaire signalé avec succès';

  @override
  String get pleaseEnterSideEffect => 'Veuillez saisir un effet secondaire';

  @override
  String get keepUsing => 'Continuer à Utiliser';

  @override
  String get cancelMethod => 'Annuler la Méthode';

  @override
  String get mild => 'Léger';

  @override
  String get moderate => 'Modéré';

  @override
  String get severe => 'Sévère';

  @override
  String get rare => 'Rare';

  @override
  String get occasional => 'Occasionnel';

  @override
  String get common => 'Commun';

  @override
  String get frequent => 'Fréquent';
}
