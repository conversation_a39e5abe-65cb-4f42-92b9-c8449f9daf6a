// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Ubuzima';

  @override
  String get appTagline => 'Empowering Your Health Journey';

  @override
  String get appDescription =>
      'Your comprehensive companion for family planning, reproductive health, and wellness - designed with care for the modern woman';

  @override
  String get welcomeBack => 'Welcome Back';

  @override
  String get signInToContinue => 'Sign in to continue your health journey';

  @override
  String get email => 'Email';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get enterEmail => 'Enter your email';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterValidEmail => 'Please enter a valid email';

  @override
  String get password => 'Password';

  @override
  String get enterPassword => 'Enter your password';

  @override
  String get pleaseEnterPassword => 'Please enter your password';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get rememberMe => 'Remember me';

  @override
  String get signIn => 'Sign In';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get or => 'OR';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get createAccount => 'Create Account';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get bySigningIn => 'By signing in, you agree to our';

  @override
  String get and => ' and ';

  @override
  String get goodMorning => 'Good morning';

  @override
  String get goodAfternoon => 'Good afternoon';

  @override
  String get goodEvening => 'Good evening';

  @override
  String get administrator => 'Administrator';

  @override
  String get healthWorker => 'Health Worker';

  @override
  String get client => 'Client';

  @override
  String get loginSuccess => 'Login successful!';

  @override
  String get registrationSuccess => 'Registration successful!';

  @override
  String get updateSuccess => 'Updated successfully!';

  @override
  String get deleteSuccess => 'Deleted successfully!';

  @override
  String get consultation => 'Consultation';

  @override
  String get familyPlanning => 'Family Planning';

  @override
  String get prenatalCare => 'Prenatal Care';

  @override
  String get postnatalCare => 'Postnatal Care';

  @override
  String get vaccination => 'Vaccination';

  @override
  String get healthScreening => 'Health Screening';

  @override
  String get followUp => 'Follow Up';

  @override
  String get emergency => 'Emergency';

  @override
  String get counseling => 'Counseling';

  @override
  String get other => 'Other';

  @override
  String get onceDaily => 'Once daily';

  @override
  String get twiceDaily => 'Twice daily';

  @override
  String get threeTimes => 'Three times daily';

  @override
  String get fourTimes => 'Four times daily';

  @override
  String get asNeeded => 'As needed';

  @override
  String get weekly => 'Weekly';

  @override
  String get monthly => 'Monthly';

  @override
  String get spotting => 'Spotting';

  @override
  String get light => 'Light';

  @override
  String get medium => 'Medium';

  @override
  String get heavy => 'Heavy';

  @override
  String get language => 'Language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get english => 'English';

  @override
  String get french => 'Français';

  @override
  String get kinyarwanda => 'Kinyarwanda';

  @override
  String get welcomeBackComma => 'Welcome back,';

  @override
  String get letsTrackYourHealthJourney => 'Let\'s track your health journey';

  @override
  String get appointmentManagement => 'Appointment Management';

  @override
  String get myAppointments => 'My Appointments';

  @override
  String get healthOverview => 'Health Overview';

  @override
  String get totalRecords => 'Total Records';

  @override
  String get recentRecords => 'Recent (30d)';

  @override
  String get generalConsultation => 'General Consultation';

  @override
  String get followUpVisit => 'Follow-up Visit';

  @override
  String get healthCounseling => 'Health Counseling';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get appointments => 'Appointments';

  @override
  String get healthRecords => 'Health Records';

  @override
  String get medications => 'Medications';

  @override
  String get education => 'Education';

  @override
  String get community => 'Community';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get logout => 'Logout';

  @override
  String get home => 'Home';

  @override
  String get health => 'Health';

  @override
  String get bookAppointment => 'Book Appointment';

  @override
  String get viewRecords => 'View Records';

  @override
  String get trackCycle => 'Track Cycle';

  @override
  String get aiAssistant => 'AI Assistant';

  @override
  String get readScreenContentAloud => 'Read screen content aloud';

  @override
  String get today => 'Today';

  @override
  String get upcoming => 'Upcoming';

  @override
  String get past => 'Past';

  @override
  String get all => 'All';

  @override
  String get manageSlots => 'Manage Slots';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get sort => 'Sort';

  @override
  String get refresh => 'Refresh';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get confirm => 'Confirm';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get ok => 'OK';

  @override
  String get close => 'Close';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get done => 'Done';

  @override
  String get submit => 'Submit';

  @override
  String get send => 'Send';

  @override
  String get receive => 'Receive';

  @override
  String get view => 'View';

  @override
  String get details => 'Details';

  @override
  String get more => 'More';

  @override
  String get less => 'Less';

  @override
  String get show => 'Show';

  @override
  String get hide => 'Hide';

  @override
  String get select => 'Select';

  @override
  String get choose => 'Choose';

  @override
  String get pick => 'Pick';

  @override
  String get upload => 'Upload';

  @override
  String get download => 'Download';

  @override
  String get share => 'Share';

  @override
  String get copy => 'Copy';

  @override
  String get paste => 'Paste';

  @override
  String get cut => 'Cut';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get clear => 'Clear';

  @override
  String get reset => 'Reset';

  @override
  String get update => 'Update';

  @override
  String get create => 'Create';

  @override
  String get open => 'Open';

  @override
  String get bookNewAppointment => 'Book new appointment';

  @override
  String get appointmentBooked => 'Appointment booked';

  @override
  String get appointmentCancelled => 'Appointment cancelled';

  @override
  String get appointmentConfirmed => 'Appointment confirmed';

  @override
  String get appointmentRescheduled => 'Appointment rescheduled';

  @override
  String get noAppointments => 'No appointments';

  @override
  String get noAppointmentsFound => 'No appointments found';

  @override
  String get appointmentDetails => 'Appointment details';

  @override
  String get appointmentType => 'Appointment type';

  @override
  String get appointmentDate => 'Appointment date';

  @override
  String get appointmentTime => 'Appointment time';

  @override
  String get appointmentLocation => 'Appointment location';

  @override
  String get appointmentNotes => 'Appointment notes';

  @override
  String get appointmentStatus => 'Appointment status';

  @override
  String get appointmentDuration => 'Appointment duration';

  @override
  String get appointmentReminder => 'Appointment reminder';

  @override
  String get rescheduleAppointment => 'Reschedule appointment';

  @override
  String get cancelAppointment => 'Cancel appointment';

  @override
  String get confirmAppointment => 'Confirm appointment';

  @override
  String get editAppointment => 'Edit appointment';

  @override
  String get deleteAppointment => 'Delete appointment';

  @override
  String get viewAppointment => 'View appointment';

  @override
  String get searchAppointments => 'Search appointments';

  @override
  String get filterAppointments => 'Filter appointments';

  @override
  String get sortAppointments => 'Sort appointments';

  @override
  String get exportAppointments => 'Export appointments';

  @override
  String get printAppointments => 'Print appointments';

  @override
  String get shareAppointments => 'Share appointments';

  @override
  String get syncAppointments => 'Sync appointments';

  @override
  String get backupAppointments => 'Backup appointments';

  @override
  String get restoreAppointments => 'Restore appointments';

  @override
  String get importAppointments => 'Import appointments';

  @override
  String get scheduled => 'Scheduled';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get completed => 'Completed';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get rescheduled => 'Rescheduled';

  @override
  String get pending => 'Pending';

  @override
  String get approved => 'Approved';

  @override
  String get rejected => 'Rejected';

  @override
  String get inProgress => 'In Progress';

  @override
  String get onHold => 'On Hold';

  @override
  String get delayed => 'Delayed';

  @override
  String get urgent => 'Urgent';

  @override
  String get normal => 'Normal';

  @override
  String get low => 'Low';

  @override
  String get high => 'High';

  @override
  String get critical => 'Critical';

  @override
  String get routine => 'Routine';

  @override
  String get diagnosis => 'Diagnosis';

  @override
  String get treatment => 'Treatment';

  @override
  String get therapy => 'Therapy';

  @override
  String get surgery => 'Surgery';

  @override
  String get procedure => 'Procedure';

  @override
  String get test => 'Test';

  @override
  String get examination => 'Examination';

  @override
  String get visit => 'Visit';

  @override
  String get session => 'Session';

  @override
  String get assessment => 'Assessment';

  @override
  String get evaluation => 'Evaluation';

  @override
  String get review => 'Review';

  @override
  String get analysis => 'Analysis';

  @override
  String get report => 'Report';

  @override
  String get summary => 'Summary';

  @override
  String get overview => 'Overview';

  @override
  String get history => 'History';

  @override
  String get record => 'Record';

  @override
  String get file => 'File';

  @override
  String get document => 'Document';

  @override
  String get form => 'Form';

  @override
  String get application => 'Application';

  @override
  String get request => 'Request';

  @override
  String get order => 'Order';

  @override
  String get prescription => 'Prescription';

  @override
  String get medication => 'Medication';

  @override
  String get medicine => 'Medicine';

  @override
  String get drug => 'Drug';

  @override
  String get pill => 'Pill';

  @override
  String get tablet => 'Tablet';

  @override
  String get capsule => 'Capsule';

  @override
  String get injection => 'Injection';

  @override
  String get vaccine => 'Vaccine';

  @override
  String get shot => 'Shot';

  @override
  String get dose => 'Dose';

  @override
  String get dosage => 'Dosage';

  @override
  String get frequency => 'Frequency';

  @override
  String get schedule => 'Schedule';

  @override
  String get duration => 'Duration';

  @override
  String get period => 'Period';

  @override
  String get interval => 'Interval';

  @override
  String get cycle => 'Cycle';

  @override
  String get phase => 'Phase';

  @override
  String get stage => 'Stage';

  @override
  String get step => 'Step';

  @override
  String get level => 'Level';

  @override
  String get grade => 'Grade';

  @override
  String get degree => 'Degree';

  @override
  String get severity => 'Severity';

  @override
  String get intensity => 'Intensity';

  @override
  String get strength => 'Strength';

  @override
  String get wellness => 'Wellness';

  @override
  String get condition => 'Condition';

  @override
  String get state => 'State';

  @override
  String get status => 'Status';

  @override
  String get situation => 'Situation';

  @override
  String get position => 'Position';

  @override
  String get location => 'Location';

  @override
  String get place => 'Place';

  @override
  String get area => 'Area';

  @override
  String get region => 'Region';

  @override
  String get zone => 'Zone';

  @override
  String get center => 'Center';

  @override
  String get clinic => 'Clinic';

  @override
  String get hospital => 'Hospital';

  @override
  String get pharmacy => 'Pharmacy';

  @override
  String get laboratory => 'Laboratory';

  @override
  String get office => 'Office';

  @override
  String get room => 'Room';

  @override
  String get department => 'Department';

  @override
  String get unit => 'Unit';

  @override
  String get division => 'Division';

  @override
  String get section => 'Section';

  @override
  String get service => 'Service';

  @override
  String get program => 'Program';

  @override
  String get project => 'Project';

  @override
  String get plan => 'Plan';

  @override
  String get method => 'Method';

  @override
  String get technique => 'Technique';

  @override
  String get approach => 'Approach';

  @override
  String get system => 'System';

  @override
  String get network => 'Network';

  @override
  String get platform => 'Platform';

  @override
  String get contraception => 'Contraception';

  @override
  String get contraceptiveMethods => 'Contraceptive Methods';

  @override
  String get myMethods => 'My Methods';

  @override
  String get sideEffects => 'Side Effects';

  @override
  String get sideEffectsReports => 'Side Effects Reports';

  @override
  String get reportSideEffect => 'Report Side Effect';

  @override
  String get addMethod => 'Add Method';

  @override
  String get editMethod => 'Edit Method';

  @override
  String get deleteMethod => 'Delete Method';

  @override
  String get chooseMethod => 'Choose Method';

  @override
  String get methodName => 'Method Name';

  @override
  String get methodDescription => 'Method Description';

  @override
  String get effectiveness => 'Effectiveness';

  @override
  String get instructions => 'Instructions';

  @override
  String get startDate => 'Start Date';

  @override
  String get endDate => 'End Date';

  @override
  String get nextAppointment => 'Next Appointment';

  @override
  String get prescribedBy => 'Prescribed By';

  @override
  String get isActive => 'Is Active';

  @override
  String get additionalNotes => 'Additional Notes';

  @override
  String get birthControlPills => 'Birth Control Pills';

  @override
  String get implant => 'Implant';

  @override
  String get iud => 'IUD';

  @override
  String get condoms => 'Condoms';

  @override
  String get diaphragm => 'Diaphragm';

  @override
  String get patch => 'Patch';

  @override
  String get ring => 'Ring';

  @override
  String get naturalFamilyPlanning => 'Natural Family Planning';

  @override
  String get sterilization => 'Sterilization';

  @override
  String get emergencyContraception => 'Emergency Contraception';

  @override
  String get hormonalMethods => 'Hormonal Methods';

  @override
  String get barrierMethods => 'Barrier Methods';

  @override
  String get naturalMethods => 'Natural Methods';

  @override
  String get permanentMethods => 'Permanent Methods';

  @override
  String get otherMethods => 'Other Methods';

  @override
  String get sideEffectName => 'Side Effect';

  @override
  String get description => 'Description';

  @override
  String get dateReported => 'Date Reported';

  @override
  String get retry => 'Retry';

  @override
  String get optional => 'Optional';

  @override
  String get pleaseSelectMethod => 'Please select a contraceptive method';

  @override
  String get sideEffectReported => 'Side effect reported successfully';

  @override
  String get pleaseEnterSideEffect => 'Please enter a side effect';

  @override
  String get keepUsing => 'Keep Using';

  @override
  String get cancelMethod => 'Cancel Method';

  @override
  String get mild => 'Mild';

  @override
  String get moderate => 'Moderate';

  @override
  String get severe => 'Severe';

  @override
  String get rare => 'Rare';

  @override
  String get occasional => 'Occasional';

  @override
  String get common => 'Common';

  @override
  String get frequent => 'Frequent';
}
