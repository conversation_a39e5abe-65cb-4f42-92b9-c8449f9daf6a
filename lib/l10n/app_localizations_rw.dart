// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Kinyarwanda (`rw`).
class AppLocalizationsRw extends AppLocalizations {
  AppLocalizationsRw([String locale = 'rw']) : super(locale);

  @override
  String get appName => 'Ubuzima';

  @override
  String get appTagline => 'Gushimangira urugendo rwawe rw\'ubuzima';

  @override
  String get appDescription =>
      'Umunyangazi wawe wuzuye mu guteganya umuryango, ubuzima bw\'imyororokere, n\'ubuzima bwiza - byakozwe n\'ubwoba ku mugore wa none';

  @override
  String get welcomeBack => 'Murakaza neza';

  @override
  String get signInToContinue =>
      'Injira kugira ngo ukomeze urugendo rwawe rw\'ubuzima';

  @override
  String get email => 'Imeyili';

  @override
  String get emailAddress => '<PERSON><PERSON><PERSON> ya imeyili';

  @override
  String get enterEmail => 'Andika imeyili yawe';

  @override
  String get pleaseEnterEmail => 'Nyamuneka andika imeyili yawe';

  @override
  String get pleaseEnterValidEmail => 'Nyamuneka andika imeyili nyayo';

  @override
  String get password => 'Ijambo ry\'ibanga';

  @override
  String get enterPassword => 'Andika ijambo ryawe ry\'ibanga';

  @override
  String get pleaseEnterPassword => 'Nyamuneka andika ijambo ryawe ry\'ibanga';

  @override
  String get passwordTooShort =>
      'Ijambo ry\'ibanga rigomba kuba rifite byibuze inyuguti 6';

  @override
  String get rememberMe => 'Nyibuka';

  @override
  String get signIn => 'Injira';

  @override
  String get forgotPassword => 'Wibagiwe ijambo ry\'ibanga?';

  @override
  String get or => 'CYANGWA';

  @override
  String get dontHaveAccount => 'Ntufite konti?';

  @override
  String get createAccount => 'Kora konti';

  @override
  String get termsOfService => 'Amabwiriza yo gukoresha';

  @override
  String get privacyPolicy => 'Politiki y\'ibanga';

  @override
  String get bySigningIn => 'Mu kwinjira, wemera amabwiriza yacu';

  @override
  String get and => ' na ';

  @override
  String get goodMorning => 'Mwaramutse';

  @override
  String get goodAfternoon => 'Mwiriwe';

  @override
  String get goodEvening => 'Muramuke';

  @override
  String get administrator => 'Umuyobozi';

  @override
  String get healthWorker => 'Umukozi w\'ubuzima';

  @override
  String get client => 'Umukiriya';

  @override
  String get loginSuccess => 'Kwinjira byagenze neza!';

  @override
  String get registrationSuccess => 'Kwiyandikisha byagenze neza!';

  @override
  String get updateSuccess => 'Kuvugurura byagenze neza!';

  @override
  String get deleteSuccess => 'Gusiba byagenze neza!';

  @override
  String get consultation => 'Ubujyanama';

  @override
  String get familyPlanning => 'Guteganya umuryango';

  @override
  String get prenatalCare => 'Kwita ku mugore utwite';

  @override
  String get postnatalCare => 'Kwita nyuma yo kubyara';

  @override
  String get vaccination => 'Gukingira';

  @override
  String get healthScreening => 'Gusuzuma ubuzima';

  @override
  String get followUp => 'Gukurikirana';

  @override
  String get emergency => 'Byihutirwa';

  @override
  String get counseling => 'Ubujyanama';

  @override
  String get other => 'Ikindi';

  @override
  String get onceDaily => 'Rimwe ku munsi';

  @override
  String get twiceDaily => 'Inshuro ebyiri ku munsi';

  @override
  String get threeTimes => 'Inshuro eshatu ku munsi';

  @override
  String get fourTimes => 'Inshuro enye ku munsi';

  @override
  String get asNeeded => 'Iyo bikenewe';

  @override
  String get weekly => 'Buri cyumweru';

  @override
  String get monthly => 'Buri kwezi';

  @override
  String get spotting => 'Amaraso make';

  @override
  String get light => 'Byoroshye';

  @override
  String get medium => 'Hagati';

  @override
  String get heavy => 'Byinshi';

  @override
  String get language => 'Ururimi';

  @override
  String get selectLanguage => 'Hitamo ururimi';

  @override
  String get english => 'Icyongereza';

  @override
  String get french => 'Igifaransa';

  @override
  String get kinyarwanda => 'Ikinyarwanda';

  @override
  String get welcomeBackComma => 'Kaze neza,';

  @override
  String get letsTrackYourHealthJourney =>
      'Reka dukurikirane urugendo rwubuzima';

  @override
  String get appointmentManagement => 'Gucunga Abakozi';

  @override
  String get myAppointments => 'Ishyirwaho ryanjye';

  @override
  String get healthOverview => 'Incamake yubuzima';

  @override
  String get totalRecords => 'Inyandiko zose';

  @override
  String get recentRecords => 'Vuba aha (30d)';

  @override
  String get generalConsultation => 'Inama rusange';

  @override
  String get followUpVisit => 'Gusura';

  @override
  String get healthCounseling => 'Ubujyanama ku buzima';

  @override
  String get dashboard => 'Ikibaho';

  @override
  String get appointments => 'Ishyirwaho';

  @override
  String get healthRecords => 'Inyandiko z\'ubuzima';

  @override
  String get medications => 'Imiti';

  @override
  String get education => 'Uburezi';

  @override
  String get community => 'Umuryango';

  @override
  String get profile => 'Umwirondoro';

  @override
  String get settings => 'Igenamiterere';

  @override
  String get logout => 'Kwinjira';

  @override
  String get home => 'Murugo';

  @override
  String get health => 'Ubuzima';

  @override
  String get bookAppointment => 'Ishyirwaho ry\'ibitabo';

  @override
  String get viewRecords => 'Reba inyandiko';

  @override
  String get trackCycle => 'Kurikirana Cycle';

  @override
  String get aiAssistant => 'Umufasha wa AI';

  @override
  String get readScreenContentAloud => 'Soma ibiri mu ijwi riranguruye';

  @override
  String get today => 'Uyu munsi';

  @override
  String get upcoming => 'Ibizaza';

  @override
  String get past => 'Kera';

  @override
  String get all => 'Byose';

  @override
  String get manageSlots => 'Gucunga Ahantu';

  @override
  String get save => 'Bika';

  @override
  String get cancel => 'Kureka';

  @override
  String get delete => 'Siba';

  @override
  String get edit => 'Hindura';

  @override
  String get add => 'Ongeraho';

  @override
  String get search => 'Shakisha';

  @override
  String get filter => 'Muyunguruzi';

  @override
  String get sort => 'Sort';

  @override
  String get refresh => 'Ongera';

  @override
  String get loading => 'Kuremera ...';

  @override
  String get error => 'Ikosa';

  @override
  String get success => 'Intsinzi';

  @override
  String get warning => 'Iburira';

  @override
  String get info => 'Amakuru';

  @override
  String get confirm => 'Emeza';

  @override
  String get yes => 'Yego';

  @override
  String get no => 'Oya';

  @override
  String get ok => 'Nibyo';

  @override
  String get close => 'Funga';

  @override
  String get back => 'Inyuma';

  @override
  String get next => 'Ibikurikira';

  @override
  String get previous => 'Mbere';

  @override
  String get done => 'Byakozwe';

  @override
  String get submit => 'Tanga';

  @override
  String get send => 'Ohereza';

  @override
  String get receive => 'Akira';

  @override
  String get view => 'Reba';

  @override
  String get details => 'Ibisobanuro';

  @override
  String get more => 'Ibindi';

  @override
  String get less => 'Bike';

  @override
  String get show => 'Erekana';

  @override
  String get hide => 'Hisha';

  @override
  String get select => 'Hitamo';

  @override
  String get choose => 'Hitamo';

  @override
  String get pick => 'Tora';

  @override
  String get upload => 'Kuramo';

  @override
  String get download => 'Kuramo';

  @override
  String get share => 'Sangira';

  @override
  String get copy => 'Gukoporora';

  @override
  String get paste => 'Shyira';

  @override
  String get cut => 'Kata';

  @override
  String get undo => 'Kuraho';

  @override
  String get redo => 'Ongera';

  @override
  String get clear => 'Biragaragara';

  @override
  String get reset => 'Gusubiramo';

  @override
  String get update => 'Kuvugurura';

  @override
  String get create => 'Kurema';

  @override
  String get open => 'Fungura';

  @override
  String get bookNewAppointment => 'Andika gahunda nshya';

  @override
  String get appointmentBooked => 'Ishyirwaho ryanditse';

  @override
  String get appointmentCancelled => 'Ishyirwaho ryahagaritswe';

  @override
  String get appointmentConfirmed => 'Ishyirwaho ryemejwe';

  @override
  String get appointmentRescheduled => 'Ishyirwaho ryimuriwe';

  @override
  String get noAppointments => 'Nta gahunda';

  @override
  String get noAppointmentsFound => 'Nta gahunda zabonetse';

  @override
  String get appointmentDetails => 'Ibisobanuro birambuye';

  @override
  String get appointmentType => 'Ubwoko bw\'abakozi';

  @override
  String get appointmentDate => 'Itariki yo gushyirwaho';

  @override
  String get appointmentTime => 'Igihe cyo gushyirwaho';

  @override
  String get appointmentLocation => 'Ahantu ho gushyirwaho';

  @override
  String get appointmentNotes => 'Inyandiko zishyirwaho';

  @override
  String get appointmentStatus => 'Imiterere';

  @override
  String get appointmentDuration => 'Igihe cyo gushyirwaho';

  @override
  String get appointmentReminder => 'Kwibutsa ishyirwaho';

  @override
  String get rescheduleAppointment => 'Hindura gahunda';

  @override
  String get cancelAppointment => 'Hagarika gahunda';

  @override
  String get confirmAppointment => 'Emeza gahunda';

  @override
  String get editAppointment => 'Hindura gahunda';

  @override
  String get deleteAppointment => 'Siba gahunda';

  @override
  String get viewAppointment => 'Reba gahunda';

  @override
  String get searchAppointments => 'Shakisha gahunda';

  @override
  String get filterAppointments => 'Shungura gahunda';

  @override
  String get sortAppointments => 'Gutondekanya gahunda';

  @override
  String get exportAppointments => 'Kohereza ibicuruzwa hanze';

  @override
  String get printAppointments => 'Andika gahunda';

  @override
  String get shareAppointments => 'Sangira gahunda';

  @override
  String get syncAppointments => 'Guhuza gahunda';

  @override
  String get backupAppointments => 'Gusubiramo gahunda';

  @override
  String get restoreAppointments => 'Kugarura gahunda';

  @override
  String get importAppointments => 'Kuzana gahunda';

  @override
  String get scheduled => 'Gahunda';

  @override
  String get confirmed => 'Byemejwe';

  @override
  String get completed => 'Byarangiye';

  @override
  String get cancelled => 'Yahagaritswe';

  @override
  String get rescheduled => 'Yongeye kwimurwa';

  @override
  String get pending => 'Bitegereje';

  @override
  String get approved => 'Byemejwe';

  @override
  String get rejected => 'Yanze';

  @override
  String get inProgress => 'Iterambere';

  @override
  String get onHold => 'Hagarara';

  @override
  String get delayed => 'Gutinda';

  @override
  String get urgent => 'Byihutirwa';

  @override
  String get normal => 'Bisanzwe';

  @override
  String get low => 'Hasi';

  @override
  String get high => 'Hejuru';

  @override
  String get critical => 'Birakomeye';

  @override
  String get routine => 'Inzira';

  @override
  String get diagnosis => 'Gusuzuma';

  @override
  String get treatment => 'Umuti';

  @override
  String get therapy => 'Ubuvuzi';

  @override
  String get surgery => 'Kubaga';

  @override
  String get procedure => 'Inzira';

  @override
  String get test => 'Ikizamini';

  @override
  String get examination => 'Ikizamini';

  @override
  String get visit => 'Sura';

  @override
  String get session => 'Isomo';

  @override
  String get assessment => 'Isuzuma';

  @override
  String get evaluation => 'Isuzuma';

  @override
  String get review => 'Isubiramo';

  @override
  String get analysis => 'Isesengura';

  @override
  String get report => 'Raporo';

  @override
  String get summary => 'Incamake';

  @override
  String get overview => 'Incamake';

  @override
  String get history => 'Amateka';

  @override
  String get record => 'Andika';

  @override
  String get file => 'Idosiye';

  @override
  String get document => 'Inyandiko';

  @override
  String get form => 'Ifishi';

  @override
  String get application => 'Gusaba';

  @override
  String get request => 'Gusaba';

  @override
  String get order => 'Tegeka';

  @override
  String get prescription => 'Urwandiko';

  @override
  String get medication => 'Imiti';

  @override
  String get medicine => 'Ubuvuzi';

  @override
  String get drug => 'Ibiyobyabwenge';

  @override
  String get pill => 'Inkingi';

  @override
  String get tablet => 'Tablet';

  @override
  String get capsule => 'Capsule';

  @override
  String get injection => 'Gutera inshinge';

  @override
  String get vaccine => 'Urukingo';

  @override
  String get shot => 'Kurasa';

  @override
  String get dose => 'Dose';

  @override
  String get dosage => 'Umubare';

  @override
  String get frequency => 'Inshuro';

  @override
  String get schedule => 'Gahunda';

  @override
  String get duration => 'Ikiringo';

  @override
  String get period => 'Ikiringo';

  @override
  String get interval => 'Intera';

  @override
  String get cycle => 'Ukuzenguruka';

  @override
  String get phase => 'Icyiciro';

  @override
  String get stage => 'Icyiciro';

  @override
  String get step => 'Intambwe';

  @override
  String get level => 'Urwego';

  @override
  String get grade => 'Icyiciro';

  @override
  String get degree => 'Impamyabumenyi';

  @override
  String get severity => 'Uburemere';

  @override
  String get intensity => 'Ubukomezi';

  @override
  String get strength => 'Imbaraga';

  @override
  String get wellness => 'Kumererwa neza';

  @override
  String get condition => 'Imiterere';

  @override
  String get state => 'Leta';

  @override
  String get status => 'Imiterere';

  @override
  String get situation => 'Imimerere';

  @override
  String get position => 'Umwanya';

  @override
  String get location => 'Aho biherereye';

  @override
  String get place => 'Ikibanza';

  @override
  String get area => 'Agace';

  @override
  String get region => 'Intara';

  @override
  String get zone => 'Zone';

  @override
  String get center => 'Ikigo';

  @override
  String get clinic => 'Ivuriro';

  @override
  String get hospital => 'Ibitaro';

  @override
  String get pharmacy => 'Farumasi';

  @override
  String get laboratory => 'Laboratoire';

  @override
  String get office => 'Ibiro';

  @override
  String get room => 'Icyumba';

  @override
  String get department => 'Ishami';

  @override
  String get unit => 'Igice';

  @override
  String get division => 'Igabana';

  @override
  String get section => 'Icyiciro';

  @override
  String get service => 'Serivisi';

  @override
  String get program => 'Gahunda';

  @override
  String get project => 'Umushinga';

  @override
  String get plan => 'Tegura';

  @override
  String get method => 'Uburyo';

  @override
  String get technique => 'Ubuhanga';

  @override
  String get approach => 'Inzira';

  @override
  String get system => 'Sisitemu';

  @override
  String get network => 'Umuyoboro';

  @override
  String get platform => 'Ihuriro';

  @override
  String get contraception => 'Kuringaniza imbyaro';

  @override
  String get contraceptiveMethods => 'Uburyo bwo kuboneza urubyaro';

  @override
  String get myMethods => 'Uburyo bwanjye';

  @override
  String get sideEffects => 'Ingaruka Zuruhande';

  @override
  String get sideEffectsReports => 'Raporo Yuruhande Raporo';

  @override
  String get reportSideEffect => 'Raporo Ingaruka';

  @override
  String get addMethod => 'Ongeraho Uburyo';

  @override
  String get editMethod => 'Hindura Uburyo';

  @override
  String get deleteMethod => 'Gusiba Uburyo';

  @override
  String get chooseMethod => 'Hitamo Uburyo';

  @override
  String get methodName => 'Izina ryuburyo';

  @override
  String get methodDescription => 'Uburyo bwo gusobanura';

  @override
  String get effectiveness => 'Gukora neza';

  @override
  String get instructions => 'Amabwiriza';

  @override
  String get startDate => 'Itariki yo gutangiriraho';

  @override
  String get endDate => 'Itariki yo kurangiriraho';

  @override
  String get nextAppointment => 'Igenamigambi ritaha';

  @override
  String get prescribedBy => 'Byanditswe na';

  @override
  String get isActive => 'Nibikorwa';

  @override
  String get additionalNotes => 'Inyandiko z\'inyongera';

  @override
  String get birthControlPills => 'Ibinini byo kuboneza urubyaro';

  @override
  String get implant => 'Kwimura';

  @override
  String get iud => 'IUD';

  @override
  String get condoms => 'Udukingirizo';

  @override
  String get diaphragm => 'Diaphragm';

  @override
  String get patch => 'Patch';

  @override
  String get ring => 'Impeta';

  @override
  String get naturalFamilyPlanning => 'Kuboneza urubyaro bisanzwe';

  @override
  String get sterilization => 'Kurimbuka';

  @override
  String get emergencyContraception => 'Kuringaniza imbyaro byihutirwa';

  @override
  String get hormonalMethods => 'Uburyo bwa Hormonal';

  @override
  String get barrierMethods => 'Uburyo bwa bariyeri';

  @override
  String get naturalMethods => 'Uburyo bwa Kamere';

  @override
  String get permanentMethods => 'Uburyo buhoraho';

  @override
  String get otherMethods => 'Ubundi buryo';

  @override
  String get sideEffectName => 'Ingaruka Kuruhande';

  @override
  String get description => 'Ibisobanuro';

  @override
  String get dateReported => 'Itariki Yatangajwe';

  @override
  String get retry => 'Ongera ugerageze';

  @override
  String get optional => 'Bihitamo';

  @override
  String get pleaseSelectMethod =>
      'Nyamuneka hitamo uburyo bwo kuboneza urubyaro';

  @override
  String get sideEffectReported => 'Ingaruka zo kuruhande zatanzwe neza';

  @override
  String get pleaseEnterSideEffect => 'Nyamuneka andika ingaruka';

  @override
  String get keepUsing => 'Komeza Ukoreshe';

  @override
  String get cancelMethod => 'Kureka Uburyo';

  @override
  String get mild => 'Ubwitonzi';

  @override
  String get moderate => 'Guciriritse';

  @override
  String get severe => 'Birakabije';

  @override
  String get rare => 'Ntibisanzwe';

  @override
  String get occasional => 'Rimwe na rimwe';

  @override
  String get common => 'Bisanzwe';

  @override
  String get frequent => 'Kenshi';
}
