import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_rw.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('fr'),
    Locale('rw'),
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'Ubuzima'**
  String get appName;

  /// The tagline of the application
  ///
  /// In en, this message translates to:
  /// **'Empowering Your Health Journey'**
  String get appTagline;

  /// The description of the application
  ///
  /// In en, this message translates to:
  /// **'Your comprehensive companion for family planning, reproductive health, and wellness - designed with care for the modern woman'**
  String get appDescription;

  /// Welcome message on login screen
  ///
  /// In en, this message translates to:
  /// **'Welcome Back'**
  String get welcomeBack;

  /// Subtitle on login screen
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue your health journey'**
  String get signInToContinue;

  /// Email field label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Email address field label
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// Email field hint text
  ///
  /// In en, this message translates to:
  /// **'Enter your email'**
  String get enterEmail;

  /// Email validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get pleaseEnterEmail;

  /// Email format validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get pleaseEnterValidEmail;

  /// Password field label
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Password field hint text
  ///
  /// In en, this message translates to:
  /// **'Enter your password'**
  String get enterPassword;

  /// Password validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter your password'**
  String get pleaseEnterPassword;

  /// Password length validation error
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordTooShort;

  /// Remember me checkbox label
  ///
  /// In en, this message translates to:
  /// **'Remember me'**
  String get rememberMe;

  /// Sign in button text
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// Forgot password link text
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPassword;

  /// Divider text between login options
  ///
  /// In en, this message translates to:
  /// **'OR'**
  String get or;

  /// Text asking if user doesn't have account
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccount;

  /// Create account button text
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// Terms of service link text
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// Privacy policy link text
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Terms agreement text
  ///
  /// In en, this message translates to:
  /// **'By signing in, you agree to our'**
  String get bySigningIn;

  /// Conjunction word
  ///
  /// In en, this message translates to:
  /// **' and '**
  String get and;

  /// Morning greeting
  ///
  /// In en, this message translates to:
  /// **'Good morning'**
  String get goodMorning;

  /// Afternoon greeting
  ///
  /// In en, this message translates to:
  /// **'Good afternoon'**
  String get goodAfternoon;

  /// Evening greeting
  ///
  /// In en, this message translates to:
  /// **'Good evening'**
  String get goodEvening;

  /// Administrator role display name
  ///
  /// In en, this message translates to:
  /// **'Administrator'**
  String get administrator;

  /// Health worker role display name
  ///
  /// In en, this message translates to:
  /// **'Health Worker'**
  String get healthWorker;

  /// Client role display name
  ///
  /// In en, this message translates to:
  /// **'Client'**
  String get client;

  /// Login success message
  ///
  /// In en, this message translates to:
  /// **'Login successful!'**
  String get loginSuccess;

  /// Registration success message
  ///
  /// In en, this message translates to:
  /// **'Registration successful!'**
  String get registrationSuccess;

  /// Update success message
  ///
  /// In en, this message translates to:
  /// **'Updated successfully!'**
  String get updateSuccess;

  /// Delete success message
  ///
  /// In en, this message translates to:
  /// **'Deleted successfully!'**
  String get deleteSuccess;

  /// Consultation appointment type
  ///
  /// In en, this message translates to:
  /// **'Consultation'**
  String get consultation;

  /// Family planning appointment type
  ///
  /// In en, this message translates to:
  /// **'Family Planning'**
  String get familyPlanning;

  /// Prenatal care appointment type
  ///
  /// In en, this message translates to:
  /// **'Prenatal Care'**
  String get prenatalCare;

  /// Postnatal care appointment type
  ///
  /// In en, this message translates to:
  /// **'Postnatal Care'**
  String get postnatalCare;

  /// Vaccination appointment type
  ///
  /// In en, this message translates to:
  /// **'Vaccination'**
  String get vaccination;

  /// Health screening appointment type
  ///
  /// In en, this message translates to:
  /// **'Health Screening'**
  String get healthScreening;

  /// Follow up appointment type
  ///
  /// In en, this message translates to:
  /// **'Follow Up'**
  String get followUp;

  /// Emergency appointment type
  ///
  /// In en, this message translates to:
  /// **'Emergency'**
  String get emergency;

  /// Counseling appointment type
  ///
  /// In en, this message translates to:
  /// **'Counseling'**
  String get counseling;

  /// Other appointment type
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// Once daily medication frequency
  ///
  /// In en, this message translates to:
  /// **'Once daily'**
  String get onceDaily;

  /// Twice daily medication frequency
  ///
  /// In en, this message translates to:
  /// **'Twice daily'**
  String get twiceDaily;

  /// Three times daily medication frequency
  ///
  /// In en, this message translates to:
  /// **'Three times daily'**
  String get threeTimes;

  /// Four times daily medication frequency
  ///
  /// In en, this message translates to:
  /// **'Four times daily'**
  String get fourTimes;

  /// As needed medication frequency
  ///
  /// In en, this message translates to:
  /// **'As needed'**
  String get asNeeded;

  /// Weekly medication frequency
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get weekly;

  /// Monthly medication frequency
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// Spotting menstrual flow type
  ///
  /// In en, this message translates to:
  /// **'Spotting'**
  String get spotting;

  /// Light menstrual flow type
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get light;

  /// Medium menstrual flow type
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// Heavy menstrual flow type
  ///
  /// In en, this message translates to:
  /// **'Heavy'**
  String get heavy;

  /// Language selector label
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Language selector title
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// English language name
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// French language name
  ///
  /// In en, this message translates to:
  /// **'Français'**
  String get french;

  /// Kinyarwanda language name
  ///
  /// In en, this message translates to:
  /// **'Kinyarwanda'**
  String get kinyarwanda;

  /// Welcome back greeting with comma
  ///
  /// In en, this message translates to:
  /// **'Welcome back,'**
  String get welcomeBackComma;

  /// Health journey tracking message
  ///
  /// In en, this message translates to:
  /// **'Let\'s track your health journey'**
  String get letsTrackYourHealthJourney;

  /// Appointment management title
  ///
  /// In en, this message translates to:
  /// **'Appointment Management'**
  String get appointmentManagement;

  /// My appointments title
  ///
  /// In en, this message translates to:
  /// **'My Appointments'**
  String get myAppointments;

  /// Health overview section title
  ///
  /// In en, this message translates to:
  /// **'Health Overview'**
  String get healthOverview;

  /// Total records label
  ///
  /// In en, this message translates to:
  /// **'Total Records'**
  String get totalRecords;

  /// Recent records label
  ///
  /// In en, this message translates to:
  /// **'Recent (30d)'**
  String get recentRecords;

  /// General consultation appointment type
  ///
  /// In en, this message translates to:
  /// **'General Consultation'**
  String get generalConsultation;

  /// Follow-up visit appointment type
  ///
  /// In en, this message translates to:
  /// **'Follow-up Visit'**
  String get followUpVisit;

  /// Health counseling appointment type
  ///
  /// In en, this message translates to:
  /// **'Health Counseling'**
  String get healthCounseling;

  /// Dashboard navigation label
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// Appointments navigation label
  ///
  /// In en, this message translates to:
  /// **'Appointments'**
  String get appointments;

  /// Health records navigation label
  ///
  /// In en, this message translates to:
  /// **'Health Records'**
  String get healthRecords;

  /// Medications navigation label
  ///
  /// In en, this message translates to:
  /// **'Medications'**
  String get medications;

  /// Education navigation label
  ///
  /// In en, this message translates to:
  /// **'Education'**
  String get education;

  /// Community navigation label
  ///
  /// In en, this message translates to:
  /// **'Community'**
  String get community;

  /// Profile navigation label
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Settings navigation label
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Logout button label
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Home tab label
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Health tab label
  ///
  /// In en, this message translates to:
  /// **'Health'**
  String get health;

  /// Book appointment button label
  ///
  /// In en, this message translates to:
  /// **'Book Appointment'**
  String get bookAppointment;

  /// View records button label
  ///
  /// In en, this message translates to:
  /// **'View Records'**
  String get viewRecords;

  /// Track cycle button label
  ///
  /// In en, this message translates to:
  /// **'Track Cycle'**
  String get trackCycle;

  /// AI assistant tooltip
  ///
  /// In en, this message translates to:
  /// **'AI Assistant'**
  String get aiAssistant;

  /// TTS button tooltip
  ///
  /// In en, this message translates to:
  /// **'Read screen content aloud'**
  String get readScreenContentAloud;

  /// Today tab label
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// Upcoming tab label
  ///
  /// In en, this message translates to:
  /// **'Upcoming'**
  String get upcoming;

  /// Past tab label
  ///
  /// In en, this message translates to:
  /// **'Past'**
  String get past;

  /// All tab label
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Manage slots tab label
  ///
  /// In en, this message translates to:
  /// **'Manage Slots'**
  String get manageSlots;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @filter.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// No description provided for @sort.
  ///
  /// In en, this message translates to:
  /// **'Sort'**
  String get sort;

  /// No description provided for @refresh.
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @warning.
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get warning;

  /// No description provided for @info.
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get info;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @previous.
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @send.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// No description provided for @receive.
  ///
  /// In en, this message translates to:
  /// **'Receive'**
  String get receive;

  /// No description provided for @view.
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get view;

  /// No description provided for @details.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// No description provided for @more.
  ///
  /// In en, this message translates to:
  /// **'More'**
  String get more;

  /// No description provided for @less.
  ///
  /// In en, this message translates to:
  /// **'Less'**
  String get less;

  /// No description provided for @show.
  ///
  /// In en, this message translates to:
  /// **'Show'**
  String get show;

  /// No description provided for @hide.
  ///
  /// In en, this message translates to:
  /// **'Hide'**
  String get hide;

  /// No description provided for @select.
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// No description provided for @choose.
  ///
  /// In en, this message translates to:
  /// **'Choose'**
  String get choose;

  /// No description provided for @pick.
  ///
  /// In en, this message translates to:
  /// **'Pick'**
  String get pick;

  /// No description provided for @upload.
  ///
  /// In en, this message translates to:
  /// **'Upload'**
  String get upload;

  /// No description provided for @download.
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// No description provided for @share.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// No description provided for @copy.
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get copy;

  /// No description provided for @paste.
  ///
  /// In en, this message translates to:
  /// **'Paste'**
  String get paste;

  /// No description provided for @cut.
  ///
  /// In en, this message translates to:
  /// **'Cut'**
  String get cut;

  /// No description provided for @undo.
  ///
  /// In en, this message translates to:
  /// **'Undo'**
  String get undo;

  /// No description provided for @redo.
  ///
  /// In en, this message translates to:
  /// **'Redo'**
  String get redo;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @reset.
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @create.
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// No description provided for @open.
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get open;

  /// No description provided for @bookNewAppointment.
  ///
  /// In en, this message translates to:
  /// **'Book new appointment'**
  String get bookNewAppointment;

  /// No description provided for @appointmentBooked.
  ///
  /// In en, this message translates to:
  /// **'Appointment booked'**
  String get appointmentBooked;

  /// No description provided for @appointmentCancelled.
  ///
  /// In en, this message translates to:
  /// **'Appointment cancelled'**
  String get appointmentCancelled;

  /// No description provided for @appointmentConfirmed.
  ///
  /// In en, this message translates to:
  /// **'Appointment confirmed'**
  String get appointmentConfirmed;

  /// No description provided for @appointmentRescheduled.
  ///
  /// In en, this message translates to:
  /// **'Appointment rescheduled'**
  String get appointmentRescheduled;

  /// No description provided for @noAppointments.
  ///
  /// In en, this message translates to:
  /// **'No appointments'**
  String get noAppointments;

  /// No description provided for @noAppointmentsFound.
  ///
  /// In en, this message translates to:
  /// **'No appointments found'**
  String get noAppointmentsFound;

  /// No description provided for @appointmentDetails.
  ///
  /// In en, this message translates to:
  /// **'Appointment details'**
  String get appointmentDetails;

  /// No description provided for @appointmentType.
  ///
  /// In en, this message translates to:
  /// **'Appointment type'**
  String get appointmentType;

  /// No description provided for @appointmentDate.
  ///
  /// In en, this message translates to:
  /// **'Appointment date'**
  String get appointmentDate;

  /// No description provided for @appointmentTime.
  ///
  /// In en, this message translates to:
  /// **'Appointment time'**
  String get appointmentTime;

  /// No description provided for @appointmentLocation.
  ///
  /// In en, this message translates to:
  /// **'Appointment location'**
  String get appointmentLocation;

  /// No description provided for @appointmentNotes.
  ///
  /// In en, this message translates to:
  /// **'Appointment notes'**
  String get appointmentNotes;

  /// No description provided for @appointmentStatus.
  ///
  /// In en, this message translates to:
  /// **'Appointment status'**
  String get appointmentStatus;

  /// No description provided for @appointmentDuration.
  ///
  /// In en, this message translates to:
  /// **'Appointment duration'**
  String get appointmentDuration;

  /// No description provided for @appointmentReminder.
  ///
  /// In en, this message translates to:
  /// **'Appointment reminder'**
  String get appointmentReminder;

  /// No description provided for @rescheduleAppointment.
  ///
  /// In en, this message translates to:
  /// **'Reschedule appointment'**
  String get rescheduleAppointment;

  /// No description provided for @cancelAppointment.
  ///
  /// In en, this message translates to:
  /// **'Cancel appointment'**
  String get cancelAppointment;

  /// No description provided for @confirmAppointment.
  ///
  /// In en, this message translates to:
  /// **'Confirm appointment'**
  String get confirmAppointment;

  /// No description provided for @editAppointment.
  ///
  /// In en, this message translates to:
  /// **'Edit appointment'**
  String get editAppointment;

  /// No description provided for @deleteAppointment.
  ///
  /// In en, this message translates to:
  /// **'Delete appointment'**
  String get deleteAppointment;

  /// No description provided for @viewAppointment.
  ///
  /// In en, this message translates to:
  /// **'View appointment'**
  String get viewAppointment;

  /// No description provided for @searchAppointments.
  ///
  /// In en, this message translates to:
  /// **'Search appointments'**
  String get searchAppointments;

  /// No description provided for @filterAppointments.
  ///
  /// In en, this message translates to:
  /// **'Filter appointments'**
  String get filterAppointments;

  /// No description provided for @sortAppointments.
  ///
  /// In en, this message translates to:
  /// **'Sort appointments'**
  String get sortAppointments;

  /// No description provided for @exportAppointments.
  ///
  /// In en, this message translates to:
  /// **'Export appointments'**
  String get exportAppointments;

  /// No description provided for @printAppointments.
  ///
  /// In en, this message translates to:
  /// **'Print appointments'**
  String get printAppointments;

  /// No description provided for @shareAppointments.
  ///
  /// In en, this message translates to:
  /// **'Share appointments'**
  String get shareAppointments;

  /// No description provided for @syncAppointments.
  ///
  /// In en, this message translates to:
  /// **'Sync appointments'**
  String get syncAppointments;

  /// No description provided for @backupAppointments.
  ///
  /// In en, this message translates to:
  /// **'Backup appointments'**
  String get backupAppointments;

  /// No description provided for @restoreAppointments.
  ///
  /// In en, this message translates to:
  /// **'Restore appointments'**
  String get restoreAppointments;

  /// No description provided for @importAppointments.
  ///
  /// In en, this message translates to:
  /// **'Import appointments'**
  String get importAppointments;

  /// No description provided for @scheduled.
  ///
  /// In en, this message translates to:
  /// **'Scheduled'**
  String get scheduled;

  /// No description provided for @confirmed.
  ///
  /// In en, this message translates to:
  /// **'Confirmed'**
  String get confirmed;

  /// No description provided for @completed.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// No description provided for @cancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// No description provided for @rescheduled.
  ///
  /// In en, this message translates to:
  /// **'Rescheduled'**
  String get rescheduled;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// No description provided for @approved.
  ///
  /// In en, this message translates to:
  /// **'Approved'**
  String get approved;

  /// No description provided for @rejected.
  ///
  /// In en, this message translates to:
  /// **'Rejected'**
  String get rejected;

  /// No description provided for @inProgress.
  ///
  /// In en, this message translates to:
  /// **'In Progress'**
  String get inProgress;

  /// No description provided for @onHold.
  ///
  /// In en, this message translates to:
  /// **'On Hold'**
  String get onHold;

  /// No description provided for @delayed.
  ///
  /// In en, this message translates to:
  /// **'Delayed'**
  String get delayed;

  /// No description provided for @urgent.
  ///
  /// In en, this message translates to:
  /// **'Urgent'**
  String get urgent;

  /// No description provided for @normal.
  ///
  /// In en, this message translates to:
  /// **'Normal'**
  String get normal;

  /// No description provided for @low.
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// No description provided for @high.
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// No description provided for @critical.
  ///
  /// In en, this message translates to:
  /// **'Critical'**
  String get critical;

  /// No description provided for @routine.
  ///
  /// In en, this message translates to:
  /// **'Routine'**
  String get routine;

  /// No description provided for @diagnosis.
  ///
  /// In en, this message translates to:
  /// **'Diagnosis'**
  String get diagnosis;

  /// No description provided for @treatment.
  ///
  /// In en, this message translates to:
  /// **'Treatment'**
  String get treatment;

  /// No description provided for @therapy.
  ///
  /// In en, this message translates to:
  /// **'Therapy'**
  String get therapy;

  /// No description provided for @surgery.
  ///
  /// In en, this message translates to:
  /// **'Surgery'**
  String get surgery;

  /// No description provided for @procedure.
  ///
  /// In en, this message translates to:
  /// **'Procedure'**
  String get procedure;

  /// No description provided for @test.
  ///
  /// In en, this message translates to:
  /// **'Test'**
  String get test;

  /// No description provided for @examination.
  ///
  /// In en, this message translates to:
  /// **'Examination'**
  String get examination;

  /// No description provided for @visit.
  ///
  /// In en, this message translates to:
  /// **'Visit'**
  String get visit;

  /// No description provided for @session.
  ///
  /// In en, this message translates to:
  /// **'Session'**
  String get session;

  /// No description provided for @assessment.
  ///
  /// In en, this message translates to:
  /// **'Assessment'**
  String get assessment;

  /// No description provided for @evaluation.
  ///
  /// In en, this message translates to:
  /// **'Evaluation'**
  String get evaluation;

  /// No description provided for @review.
  ///
  /// In en, this message translates to:
  /// **'Review'**
  String get review;

  /// No description provided for @analysis.
  ///
  /// In en, this message translates to:
  /// **'Analysis'**
  String get analysis;

  /// No description provided for @report.
  ///
  /// In en, this message translates to:
  /// **'Report'**
  String get report;

  /// No description provided for @summary.
  ///
  /// In en, this message translates to:
  /// **'Summary'**
  String get summary;

  /// No description provided for @overview.
  ///
  /// In en, this message translates to:
  /// **'Overview'**
  String get overview;

  /// No description provided for @history.
  ///
  /// In en, this message translates to:
  /// **'History'**
  String get history;

  /// No description provided for @record.
  ///
  /// In en, this message translates to:
  /// **'Record'**
  String get record;

  /// No description provided for @file.
  ///
  /// In en, this message translates to:
  /// **'File'**
  String get file;

  /// No description provided for @document.
  ///
  /// In en, this message translates to:
  /// **'Document'**
  String get document;

  /// No description provided for @form.
  ///
  /// In en, this message translates to:
  /// **'Form'**
  String get form;

  /// No description provided for @application.
  ///
  /// In en, this message translates to:
  /// **'Application'**
  String get application;

  /// No description provided for @request.
  ///
  /// In en, this message translates to:
  /// **'Request'**
  String get request;

  /// No description provided for @order.
  ///
  /// In en, this message translates to:
  /// **'Order'**
  String get order;

  /// No description provided for @prescription.
  ///
  /// In en, this message translates to:
  /// **'Prescription'**
  String get prescription;

  /// No description provided for @medication.
  ///
  /// In en, this message translates to:
  /// **'Medication'**
  String get medication;

  /// No description provided for @medicine.
  ///
  /// In en, this message translates to:
  /// **'Medicine'**
  String get medicine;

  /// No description provided for @drug.
  ///
  /// In en, this message translates to:
  /// **'Drug'**
  String get drug;

  /// No description provided for @pill.
  ///
  /// In en, this message translates to:
  /// **'Pill'**
  String get pill;

  /// No description provided for @tablet.
  ///
  /// In en, this message translates to:
  /// **'Tablet'**
  String get tablet;

  /// No description provided for @capsule.
  ///
  /// In en, this message translates to:
  /// **'Capsule'**
  String get capsule;

  /// Injection contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Injection'**
  String get injection;

  /// No description provided for @vaccine.
  ///
  /// In en, this message translates to:
  /// **'Vaccine'**
  String get vaccine;

  /// No description provided for @shot.
  ///
  /// In en, this message translates to:
  /// **'Shot'**
  String get shot;

  /// No description provided for @dose.
  ///
  /// In en, this message translates to:
  /// **'Dose'**
  String get dose;

  /// No description provided for @dosage.
  ///
  /// In en, this message translates to:
  /// **'Dosage'**
  String get dosage;

  /// Side effect frequency
  ///
  /// In en, this message translates to:
  /// **'Frequency'**
  String get frequency;

  /// No description provided for @schedule.
  ///
  /// In en, this message translates to:
  /// **'Schedule'**
  String get schedule;

  /// No description provided for @duration.
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get duration;

  /// No description provided for @period.
  ///
  /// In en, this message translates to:
  /// **'Period'**
  String get period;

  /// No description provided for @interval.
  ///
  /// In en, this message translates to:
  /// **'Interval'**
  String get interval;

  /// No description provided for @cycle.
  ///
  /// In en, this message translates to:
  /// **'Cycle'**
  String get cycle;

  /// No description provided for @phase.
  ///
  /// In en, this message translates to:
  /// **'Phase'**
  String get phase;

  /// No description provided for @stage.
  ///
  /// In en, this message translates to:
  /// **'Stage'**
  String get stage;

  /// No description provided for @step.
  ///
  /// In en, this message translates to:
  /// **'Step'**
  String get step;

  /// No description provided for @level.
  ///
  /// In en, this message translates to:
  /// **'Level'**
  String get level;

  /// No description provided for @grade.
  ///
  /// In en, this message translates to:
  /// **'Grade'**
  String get grade;

  /// No description provided for @degree.
  ///
  /// In en, this message translates to:
  /// **'Degree'**
  String get degree;

  /// Side effect severity
  ///
  /// In en, this message translates to:
  /// **'Severity'**
  String get severity;

  /// No description provided for @intensity.
  ///
  /// In en, this message translates to:
  /// **'Intensity'**
  String get intensity;

  /// No description provided for @strength.
  ///
  /// In en, this message translates to:
  /// **'Strength'**
  String get strength;

  /// No description provided for @wellness.
  ///
  /// In en, this message translates to:
  /// **'Wellness'**
  String get wellness;

  /// No description provided for @condition.
  ///
  /// In en, this message translates to:
  /// **'Condition'**
  String get condition;

  /// No description provided for @state.
  ///
  /// In en, this message translates to:
  /// **'State'**
  String get state;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @situation.
  ///
  /// In en, this message translates to:
  /// **'Situation'**
  String get situation;

  /// No description provided for @position.
  ///
  /// In en, this message translates to:
  /// **'Position'**
  String get position;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @place.
  ///
  /// In en, this message translates to:
  /// **'Place'**
  String get place;

  /// No description provided for @area.
  ///
  /// In en, this message translates to:
  /// **'Area'**
  String get area;

  /// No description provided for @region.
  ///
  /// In en, this message translates to:
  /// **'Region'**
  String get region;

  /// No description provided for @zone.
  ///
  /// In en, this message translates to:
  /// **'Zone'**
  String get zone;

  /// No description provided for @center.
  ///
  /// In en, this message translates to:
  /// **'Center'**
  String get center;

  /// No description provided for @clinic.
  ///
  /// In en, this message translates to:
  /// **'Clinic'**
  String get clinic;

  /// No description provided for @hospital.
  ///
  /// In en, this message translates to:
  /// **'Hospital'**
  String get hospital;

  /// No description provided for @pharmacy.
  ///
  /// In en, this message translates to:
  /// **'Pharmacy'**
  String get pharmacy;

  /// No description provided for @laboratory.
  ///
  /// In en, this message translates to:
  /// **'Laboratory'**
  String get laboratory;

  /// No description provided for @office.
  ///
  /// In en, this message translates to:
  /// **'Office'**
  String get office;

  /// No description provided for @room.
  ///
  /// In en, this message translates to:
  /// **'Room'**
  String get room;

  /// No description provided for @department.
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get department;

  /// No description provided for @unit.
  ///
  /// In en, this message translates to:
  /// **'Unit'**
  String get unit;

  /// No description provided for @division.
  ///
  /// In en, this message translates to:
  /// **'Division'**
  String get division;

  /// No description provided for @section.
  ///
  /// In en, this message translates to:
  /// **'Section'**
  String get section;

  /// No description provided for @service.
  ///
  /// In en, this message translates to:
  /// **'Service'**
  String get service;

  /// No description provided for @program.
  ///
  /// In en, this message translates to:
  /// **'Program'**
  String get program;

  /// No description provided for @project.
  ///
  /// In en, this message translates to:
  /// **'Project'**
  String get project;

  /// No description provided for @plan.
  ///
  /// In en, this message translates to:
  /// **'Plan'**
  String get plan;

  /// No description provided for @method.
  ///
  /// In en, this message translates to:
  /// **'Method'**
  String get method;

  /// No description provided for @technique.
  ///
  /// In en, this message translates to:
  /// **'Technique'**
  String get technique;

  /// No description provided for @approach.
  ///
  /// In en, this message translates to:
  /// **'Approach'**
  String get approach;

  /// No description provided for @system.
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get system;

  /// No description provided for @network.
  ///
  /// In en, this message translates to:
  /// **'Network'**
  String get network;

  /// No description provided for @platform.
  ///
  /// In en, this message translates to:
  /// **'Platform'**
  String get platform;

  /// Contraception section title
  ///
  /// In en, this message translates to:
  /// **'Contraception'**
  String get contraception;

  /// Contraceptive methods screen title
  ///
  /// In en, this message translates to:
  /// **'Contraceptive Methods'**
  String get contraceptiveMethods;

  /// User's contraceptive methods tab
  ///
  /// In en, this message translates to:
  /// **'My Methods'**
  String get myMethods;

  /// Side effects tab/section
  ///
  /// In en, this message translates to:
  /// **'Side Effects'**
  String get sideEffects;

  /// Side effects reports for health workers
  ///
  /// In en, this message translates to:
  /// **'Side Effects Reports'**
  String get sideEffectsReports;

  /// Report side effect button
  ///
  /// In en, this message translates to:
  /// **'Report Side Effect'**
  String get reportSideEffect;

  /// Add contraceptive method button
  ///
  /// In en, this message translates to:
  /// **'Add Method'**
  String get addMethod;

  /// Edit contraceptive method button
  ///
  /// In en, this message translates to:
  /// **'Edit Method'**
  String get editMethod;

  /// Delete contraceptive method button
  ///
  /// In en, this message translates to:
  /// **'Delete Method'**
  String get deleteMethod;

  /// Choose contraceptive method button
  ///
  /// In en, this message translates to:
  /// **'Choose Method'**
  String get chooseMethod;

  /// Contraceptive method name field
  ///
  /// In en, this message translates to:
  /// **'Method Name'**
  String get methodName;

  /// Contraceptive method description field
  ///
  /// In en, this message translates to:
  /// **'Method Description'**
  String get methodDescription;

  /// Contraceptive method effectiveness
  ///
  /// In en, this message translates to:
  /// **'Effectiveness'**
  String get effectiveness;

  /// Contraceptive method instructions
  ///
  /// In en, this message translates to:
  /// **'Instructions'**
  String get instructions;

  /// Contraceptive method start date
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// Contraceptive method end date
  ///
  /// In en, this message translates to:
  /// **'End Date'**
  String get endDate;

  /// Next appointment date
  ///
  /// In en, this message translates to:
  /// **'Next Appointment'**
  String get nextAppointment;

  /// Who prescribed the method
  ///
  /// In en, this message translates to:
  /// **'Prescribed By'**
  String get prescribedBy;

  /// Whether the method is currently active
  ///
  /// In en, this message translates to:
  /// **'Is Active'**
  String get isActive;

  /// Additional notes field
  ///
  /// In en, this message translates to:
  /// **'Additional Notes'**
  String get additionalNotes;

  /// Birth control pills contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Birth Control Pills'**
  String get birthControlPills;

  /// Implant contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Implant'**
  String get implant;

  /// IUD contraceptive method
  ///
  /// In en, this message translates to:
  /// **'IUD'**
  String get iud;

  /// Condoms contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Condoms'**
  String get condoms;

  /// Diaphragm contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Diaphragm'**
  String get diaphragm;

  /// Patch contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Patch'**
  String get patch;

  /// Ring contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Ring'**
  String get ring;

  /// Natural family planning contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Natural Family Planning'**
  String get naturalFamilyPlanning;

  /// Sterilization contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Sterilization'**
  String get sterilization;

  /// Emergency contraception method
  ///
  /// In en, this message translates to:
  /// **'Emergency Contraception'**
  String get emergencyContraception;

  /// Hormonal contraceptive methods category
  ///
  /// In en, this message translates to:
  /// **'Hormonal Methods'**
  String get hormonalMethods;

  /// Barrier contraceptive methods category
  ///
  /// In en, this message translates to:
  /// **'Barrier Methods'**
  String get barrierMethods;

  /// Natural contraceptive methods category
  ///
  /// In en, this message translates to:
  /// **'Natural Methods'**
  String get naturalMethods;

  /// Permanent contraceptive methods category
  ///
  /// In en, this message translates to:
  /// **'Permanent Methods'**
  String get permanentMethods;

  /// Other contraceptive methods category
  ///
  /// In en, this message translates to:
  /// **'Other Methods'**
  String get otherMethods;

  /// Label for side effect name field
  ///
  /// In en, this message translates to:
  /// **'Side Effect'**
  String get sideEffectName;

  /// Description field
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Date when side effect was reported
  ///
  /// In en, this message translates to:
  /// **'Date Reported'**
  String get dateReported;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Optional field indicator
  ///
  /// In en, this message translates to:
  /// **'Optional'**
  String get optional;

  /// Validation message for method selection
  ///
  /// In en, this message translates to:
  /// **'Please select a contraceptive method'**
  String get pleaseSelectMethod;

  /// Success message for side effect reporting
  ///
  /// In en, this message translates to:
  /// **'Side effect reported successfully'**
  String get sideEffectReported;

  /// Validation message for side effect input
  ///
  /// In en, this message translates to:
  /// **'Please enter a side effect'**
  String get pleaseEnterSideEffect;

  /// Button text to continue using contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Keep Using'**
  String get keepUsing;

  /// Button text to cancel contraceptive method
  ///
  /// In en, this message translates to:
  /// **'Cancel Method'**
  String get cancelMethod;

  /// Mild severity level
  ///
  /// In en, this message translates to:
  /// **'Mild'**
  String get mild;

  /// Moderate severity level
  ///
  /// In en, this message translates to:
  /// **'Moderate'**
  String get moderate;

  /// Severe severity level
  ///
  /// In en, this message translates to:
  /// **'Severe'**
  String get severe;

  /// Rare frequency
  ///
  /// In en, this message translates to:
  /// **'Rare'**
  String get rare;

  /// Occasional frequency
  ///
  /// In en, this message translates to:
  /// **'Occasional'**
  String get occasional;

  /// Common frequency
  ///
  /// In en, this message translates to:
  /// **'Common'**
  String get common;

  /// Frequent frequency
  ///
  /// In en, this message translates to:
  /// **'Frequent'**
  String get frequent;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'fr', 'rw'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'fr':
      return AppLocalizationsFr();
    case 'rw':
      return AppLocalizationsRw();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
