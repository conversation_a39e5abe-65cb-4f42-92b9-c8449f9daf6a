import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/app_constants.dart';
import '../../l10n/app_localizations.dart';

 // ✅ Correct path

/// Language state model that holds both locale and loading state
class LanguageState {
  final Locale locale;
  final bool isLoading;

  const LanguageState({
    required this.locale,
    this.isLoading = false,
  });

  LanguageState copyWith({
    Locale? locale,
    bool? isLoading,
  }) {
    return LanguageState(
      locale: locale ?? this.locale,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguageState &&
        other.locale == locale &&
        other.isLoading == isLoading;
  }

  @override
  int get hashCode => locale.hashCode ^ isLoading.hashCode;

  @override
  String toString() => 'LanguageState(locale: $locale, isLoading: $isLoading)';
}

/// Unified Language/Locale provider for managing app language
class LanguageNotifier extends StateNotifier<LanguageState> {
  LanguageNotifier() : super(const LanguageState(locale: Locale('en'))) {
    _loadSavedLanguage();
  }

  /// Use the supported locales from generated localizations (flutter_intl)
  static List<Locale> get supportedLocales => AppLocalizations.supportedLocales.toList();

  /// Load saved language from SharedPreferences
  Future<void> _loadSavedLanguage() async {
    try {
      state = state.copyWith(isLoading: true);

      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(AppConstants.languageKey) ?? 'en';

      if (_isValidLanguageCode(languageCode)) {
        state = state.copyWith(
          locale: Locale(languageCode),
          isLoading: false,
        );
      } else {
        // Reset to English if invalid language code
        await prefs.setString(AppConstants.languageKey, 'en');
        state = state.copyWith(
          locale: const Locale('en'),
          isLoading: false,
        );
      }

      debugPrint('Loaded saved language: $languageCode');
    } catch (e) {
      debugPrint('Error loading saved language: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  /// Change the locale and save it to SharedPreferences
  Future<void> setLocale(Locale locale) async {
    if (!supportedLocales.contains(locale)) {
      debugPrint('Unsupported locale: $locale');
      return;
    }

    try {
      state = state.copyWith(isLoading: true);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.languageKey, locale.languageCode);

      state = state.copyWith(
        locale: locale,
        isLoading: false,
      );

      debugPrint('Language changed to: ${locale.languageCode}');
    } catch (e) {
      debugPrint('Error saving locale: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  /// Change language by language code (main method from your original)
  Future<void> changeLanguage(String languageCode) async {
    if (!_isValidLanguageCode(languageCode)) {
      debugPrint('Invalid language code: $languageCode');
      return;
    }

    try {
      state = state.copyWith(isLoading: true);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.languageKey, languageCode);

      state = state.copyWith(locale: Locale(languageCode), isLoading: false);

      debugPrint('Language changed to: $languageCode');
    } catch (e) {
      debugPrint('Error changing language: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  /// Check if language code is supported (uses flutter_intl generated locales)
  bool _isValidLanguageCode(String code) {
    return supportedLocales.any((locale) => locale.languageCode == code);
  }

  /// Convenience methods for setting specific languages
  Future<void> setEnglish() => setLocale(const Locale('en'));
  Future<void> setFrench() => setLocale(const Locale('fr'));
  Future<void> setKinyarwanda() => setLocale(const Locale('rw'));

  /// Get language display name
  String getLanguageDisplayName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'Français';
      case 'rw':
        return 'Kinyarwanda';
      default:
        return code.toUpperCase();
    }
  }

  /// Get native language name
  String getLanguageNativeName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'Français';
      case 'rw':
        return 'Ikinyarwanda';
      default:
        return code.toUpperCase();
    }
  }

  /// Get all supported languages with metadata
  List<Map<String, String>> getSupportedLanguages() {
    return [
      {'code': 'en', 'name': 'English', 'nativeName': 'English'},
      {'code': 'fr', 'name': 'French', 'nativeName': 'Français'},
      {'code': 'rw', 'name': 'Kinyarwanda', 'nativeName': 'Ikinyarwanda'},
    ];
  }

  /// Get current language code
  String get currentLanguageCode => state.locale.languageCode;

  /// Get current language display name
  String get currentLanguageDisplayName =>
      getLanguageDisplayName(currentLanguageCode);

  /// Check if a specific language is currently selected
  bool isLanguageSelected(String languageCode) =>
      currentLanguageCode == languageCode;
}

/// Main language provider instance
final languageProvider = StateNotifierProvider<LanguageNotifier, LanguageState>(
      (ref) => LanguageNotifier(),
);

/// Helper provider to get current locale (for MaterialApp.locale)
final currentLocaleProvider = Provider<Locale>((ref) {
  return ref.watch(languageProvider).locale;
});

/// Helper provider to check if language is loading
final isLanguageLoadingProvider = Provider<bool>((ref) {
  return ref.watch(languageProvider).isLoading;
});

/// Helper provider to get current language code
final currentLanguageCodeProvider = Provider<String>((ref) {
  return ref.watch(languageProvider).locale.languageCode;
});

/// Helper provider to get language display name
final currentLanguageDisplayNameProvider = Provider<String>((ref) {
  final notifier = ref.read(languageProvider.notifier);
  final code = ref.watch(currentLanguageCodeProvider);
  return notifier.getLanguageDisplayName(code);
});

/// Helper provider to get supported languages
final supportedLanguagesProvider = Provider<List<Map<String, String>>>((ref) {
  final notifier = ref.read(languageProvider.notifier);
  return notifier.getSupportedLanguages();
});

/// Helper provider to get flutter_intl supported locales
final flutterIntlSupportedLocalesProvider = Provider<List<Locale>>((ref) {
  return LanguageNotifier.supportedLocales;
});