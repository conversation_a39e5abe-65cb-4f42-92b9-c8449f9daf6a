// Create this file: lib/core/utils/custom_material_localizations.dart

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter/cupertino.dart';

/// Custom Material Localizations Delegate that provides fallback
/// for unsupported locales like Kinyarwanda (rw)
class CustomMaterialLocalizationsDelegate
    extends LocalizationsDelegate<MaterialLocalizations> {
  const CustomMaterialLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    // Support all locales by providing fallbacks
    return true;
  }

  @override
  Future<MaterialLocalizations> load(Locale locale) async {
    // For unsupported locales like 'rw', fallback to English
    final supportedLocale = _getSupportedLocale(locale);

    print('🔧 Material Localizations: ${locale.languageCode} -> ${supportedLocale.languageCode}');

    // Load the appropriate Material localizations
    return await GlobalMaterialLocalizations.delegate.load(supportedLocale);
  }

  @override
  bool shouldReload(CustomMaterialLocalizationsDelegate old) => false;

  /// Returns a supported locale for Material components
  /// Falls back to English for unsupported locales
  Locale _getSupportedLocale(Locale locale) {
    // List of locales supported by Material components
    const supportedMaterialLocales = [
      'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',
      'ar', 'hi', 'th', 'vi', 'tr', 'pl', 'nl', 'sv', 'da', 'no',
      'fi', 'cs', 'hu', 'ro', 'bg', 'hr', 'sk', 'sl', 'et', 'lv',
      'lt', 'uk', 'be', 'mk', 'sr', 'bs', 'sq', 'mt', 'is', 'ga',
      'cy', 'eu', 'ca', 'gl', 'af', 'sw', 'zu', 'xh', 'st', 'tn',
      'ss', 've', 'ts', 'nr', 'ku', 'ky', 'kk', 'uz', 'tg', 'mn',
      'ne', 'si', 'my', 'km', 'lo', 'ka', 'am', 'ti', 'or', 'te',
      'kn', 'ml', 'ta', 'bn', 'as', 'gu', 'pa', 'ur', 'fa', 'ps',
      'sd', 'ug', 'bo', 'dz', 'yo', 'ig', 'ha', 'ff', 'wo', 'sn',
      'rn', 'ny', 'mg', 'eo', 'jv', 'su', 'ms', 'tl', 'ceb', 'haw',
      'mi', 'sm', 'to', 'fj', 'ty', 'co', 'br', 'oc', 'sc', 'rm',
    ];

    // Check if the language code is supported
    if (supportedMaterialLocales.contains(locale.languageCode)) {
      return locale;
    }

    // Special cases for common fallbacks
    switch (locale.languageCode) {
      case 'rw': // Kinyarwanda -> English
        return const Locale('en');
      case 'sw': // Swahili -> English
        return const Locale('en');
      default:
        return const Locale('en'); // Default fallback to English
    }
  }
}

/// Custom Cupertino Localizations Delegate
class CustomCupertinoLocalizationsDelegate
    extends LocalizationsDelegate<CupertinoLocalizations> {
  const CustomCupertinoLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => true;

  @override
  Future<CupertinoLocalizations> load(Locale locale) async {
    final supportedLocale = _getSupportedLocale(locale);

    print('🔧 Cupertino Localizations: ${locale.languageCode} -> ${supportedLocale.languageCode}');

    return await GlobalCupertinoLocalizations.delegate.load(supportedLocale);
  }

  @override
  bool shouldReload(CustomCupertinoLocalizationsDelegate old) => false;

  Locale _getSupportedLocale(Locale locale) {
    // Similar logic as Material localizations
    switch (locale.languageCode) {
      case 'rw':
        return const Locale('en');
      case 'sw':
        return const Locale('en');
      default:
      // For most locales, try the original first, fallback to English if needed
        return locale.languageCode.length == 2 ? locale : const Locale('en');
    }
  }
}