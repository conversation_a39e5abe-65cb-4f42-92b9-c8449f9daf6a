import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'pregnancy_plan.g.dart';

/// Pregnancy Plan Status enum
enum PregnancyPlanStatus {
  @JsonValue('PLANNING')
  planning,
  @JsonValue('TRYING')
  trying,
  @JsonValue('PREGNANT')
  pregnant,
  @JsonValue('PAUSED')
  paused,
  @JsonValue('COMPLETED')
  completed,
}

/// Pregnancy Plan model for family planning
@JsonSerializable(explicitToJson: true)
class PregnancyPlan {
  final int? id;
  final int userId;
  final User? user;
  final int? partnerId;
  final User? partner;
  final String planName;
  final DateTime? targetConceptionDate;
  final PregnancyPlanStatus currentStatus;
  final String? preconceptionGoals;
  final String? healthPreparations;
  final String? lifestyleChanges;
  final String? medicalConsultations;
  final String? progressNotes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PregnancyPlan({
    this.id,
    required this.userId,
    this.user,
    this.partnerId,
    this.partner,
    required this.planName,
    this.targetConceptionDate,
    this.currentStatus = PregnancyPlanStatus.planning,
    this.preconceptionGoals,
    this.healthPreparations,
    this.lifestyleChanges,
    this.medicalConsultations,
    this.progressNotes,
    this.createdAt,
    this.updatedAt,
  });

  /// Create from JSON
  factory PregnancyPlan.fromJson(Map<String, dynamic> json) {
    // Helper function to parse dates (same logic as User model)
    DateTime? parseDate(dynamic value) {
      if (value == null) return null;
      if (value is String) {
        return DateTime.tryParse(value);
      } else if (value is List && value.length >= 3) {
        // [year, month, day, ...]
        return DateTime(
          value[0] as int,
          value[1] as int,
          value[2] as int,
          value.length > 3 ? value[3] as int : 0,
          value.length > 4 ? value[4] as int : 0,
          value.length > 5 ? value[5] as int : 0,
          value.length > 6 ? value[6] as int : 0,
        );
      }
      return null;
    }

    return PregnancyPlan(
      id: json['id'] as int?,
      userId: (json['userId'] ?? json['user']?['id']) as int,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      partnerId: (json['partnerId'] ?? json['partner']?['id']) as int?,
      partner: json['partner'] != null
          ? User.fromJson(json['partner'] as Map<String, dynamic>)
          : null,
      planName: json['planName'] as String? ?? '',
      targetConceptionDate: parseDate(json['targetConceptionDate']),
      currentStatus: _parseStatus(json['currentStatus'] as String?),
      preconceptionGoals: json['preconceptionGoals'] as String?,
      healthPreparations: json['healthPreparations'] as String?,
      lifestyleChanges: json['lifestyleChanges'] as String?,
      medicalConsultations: json['medicalConsultations'] as String?,
      progressNotes: json['progressNotes'] as String?,
      createdAt: parseDate(json['createdAt']),
      updatedAt: parseDate(json['updatedAt']),
    );
  }

  /// Convert to JSON (uses generated helper)
  Map<String, dynamic> toJson() => _$PregnancyPlanToJson(this);

  /// Parse status from string
  static PregnancyPlanStatus _parseStatus(String? status) {
    switch (status?.toUpperCase()) {
      case 'PLANNING':
        return PregnancyPlanStatus.planning;
      case 'TRYING':
        return PregnancyPlanStatus.trying;
      case 'PREGNANT':
        return PregnancyPlanStatus.pregnant;
      case 'PAUSED':
        return PregnancyPlanStatus.paused;
      case 'COMPLETED':
        return PregnancyPlanStatus.completed;
      default:
        return PregnancyPlanStatus.planning;
    }
  }

  /// Status display name
  String get statusDisplayName {
    switch (currentStatus) {
      case PregnancyPlanStatus.planning:
        return 'Planning';
      case PregnancyPlanStatus.trying:
        return 'Trying to Conceive';
      case PregnancyPlanStatus.pregnant:
        return 'Pregnant';
      case PregnancyPlanStatus.paused:
        return 'Paused';
      case PregnancyPlanStatus.completed:
        return 'Completed';
    }
  }

  /// Status color
  String get statusColor {
    switch (currentStatus) {
      case PregnancyPlanStatus.planning:
        return '#2196F3';
      case PregnancyPlanStatus.trying:
        return '#FF9800';
      case PregnancyPlanStatus.pregnant:
        return '#4CAF50';
      case PregnancyPlanStatus.paused:
        return '#9E9E9E';
      case PregnancyPlanStatus.completed:
        return '#9C27B0';
    }
  }

  /// Check if plan is active
  bool get isActive =>
      currentStatus == PregnancyPlanStatus.planning ||
          currentStatus == PregnancyPlanStatus.trying;

  /// Days until target
  int? get daysUntilTarget {
    if (targetConceptionDate == null) return null;
    final difference = targetConceptionDate!.difference(DateTime.now()).inDays;
    return difference > 0 ? difference : 0;
  }

  /// Copy with method
  PregnancyPlan copyWith({
    int? id,
    int? userId,
    User? user,
    int? partnerId,
    User? partner,
    String? planName,
    DateTime? targetConceptionDate,
    PregnancyPlanStatus? currentStatus,
    String? preconceptionGoals,
    String? healthPreparations,
    String? lifestyleChanges,
    String? medicalConsultations,
    String? progressNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PregnancyPlan(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      user: user ?? this.user,
      partnerId: partnerId ?? this.partnerId,
      partner: partner ?? this.partner,
      planName: planName ?? this.planName,
      targetConceptionDate: targetConceptionDate ?? this.targetConceptionDate,
      currentStatus: currentStatus ?? this.currentStatus,
      preconceptionGoals: preconceptionGoals ?? this.preconceptionGoals,
      healthPreparations: healthPreparations ?? this.healthPreparations,
      lifestyleChanges: lifestyleChanges ?? this.lifestyleChanges,
      medicalConsultations: medicalConsultations ?? this.medicalConsultations,
      progressNotes: progressNotes ?? this.progressNotes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() =>
      'PregnancyPlan(id: $id, planName: $planName, status: $currentStatus)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is PregnancyPlan && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
