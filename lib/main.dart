import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'l10n/app_localizations.dart';

// Core imports
import 'core/config/app_config.dart';
import 'core/theme/app_theme.dart';
import 'core/services/api_service.dart';
import 'core/services/storage_service.dart';
import 'core/services/tts_service.dart';
import 'core/utils/app_constants.dart';
import 'core/utils/custom_material_localizations.dart';

// Feature imports
import 'features/splash/splash_screen.dart';
import 'features/auth/login_screen.dart';
import 'features/auth/register_screen.dart';
import 'features/auth/forgot_password_screen.dart';
import 'features/dashboard/role_dashboard.dart';
import 'features/dashboard/admin_dashboard.dart';
import 'features/dashboard/health_worker_dashboard.dart';
import 'features/dashboard/client_dashboard.dart';
import 'features/health_worker/health_worker_main_screen.dart';

// Admin screens
import 'features/admin/user_management_screen.dart';
import 'features/admin/client_management_screen.dart';
import 'features/admin/analytics_screen.dart';
import 'features/admin/reports_screen.dart';
import 'features/admin/content_management_screen.dart';
import 'features/admin/health_facilities_screen.dart' as admin_facilities;
import 'features/admin/system_settings_screen.dart';
import 'features/admin/file_management_screen.dart';

// Common feature screens
import 'features/profile/profile_screen.dart';
import 'features/settings/settings_screen.dart';
import 'features/settings/advanced_settings_screen.dart';
import 'features/notifications/notifications_screen.dart';
import 'features/messages/messages_tab.dart';
import 'features/appointments/appointments_screen.dart';
import 'features/health_records/health_records_screen.dart';
import 'features/medications/medications_screen.dart';
import 'features/contraception/contraception_screen.dart';
import 'features/menstrual_cycle/menstrual_cycle_screen.dart';
import 'features/sti_testing/sti_testing_screen.dart';
import 'features/education/education_screen.dart';
import 'features/support_groups/support_groups_screen.dart';
import 'features/community_events/community_events_screen.dart';
import 'features/feedback/feedback_screen.dart';
import 'features/health_facilities/health_facilities_screen.dart';

// Family Planning screens
import 'features/pregnancy/pregnancy_planning_screen.dart';
import 'features/pregnancy/due_date_calculator_screen.dart';
import 'features/pregnancy/ovulation_calculator_screen.dart';
import 'features/pregnancy/health_checklist_screen.dart';
import 'features/pregnancy/partner_management_screen.dart';
import 'features/pregnancy/partner_decisions_screen.dart';

// AI Chat
import 'features/ai_chat/screens/chat_assistant_screen.dart';

// Clinic Finder
import 'features/clinic_finder/clinic_finder_screen.dart';

// Providers
import 'core/providers/language_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 🔍 DEBUG: System locale information
  print('\n🔍 === LOCALE DEBUG INFO ===');
  print('🌍 System locale: ${WidgetsBinding.instance.platformDispatcher.locale}');
  print('🌍 System locales: ${WidgetsBinding.instance.platformDispatcher.locales}');
  print('🌍 System supported locales: ${AppLocalizations.supportedLocales}');

  // Global error handling
  FlutterError.onError = (details) {
    debugPrint('Flutter Error: ${details.exception}');
    debugPrint('Stack trace: ${details.stack}');
    FlutterError.presentError(details);
  };

  await _initializeServices();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(const ProviderScope(child: UbuzimaFamilyPlanningApp()));
}

Future<void> _initializeServices() async {
  try {
    await AppConfig.initialize();
    await StorageService.initialize();
    await ApiService.instance.initialize();

    // TTS initialization
    TTSService().initialize().then((_) {
      TTSService()
          .setKinyarwanda()
          .catchError((_) => TTSService().setEnglish());
    });

    print('✅ Services initialized');
  } catch (e) {
    print('❌ Service initialization failed: $e');
  }
}

class UbuzimaFamilyPlanningApp extends ConsumerWidget {
  const UbuzimaFamilyPlanningApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    print('\n🔄 === APP REBUILD START ===');

    final currentLocale = ref.watch(currentLocaleProvider);

    // 🔍 DEBUG: Current state
    print('🔍 Current locale from provider: $currentLocale');
    print('🔍 Current locale code: ${currentLocale.languageCode}');
    print('🔍 Current locale country: ${currentLocale.countryCode}');

    // 🔍 DEBUG: Check if provider is working
    try {
      final languageNotifier = ref.read(languageProvider.notifier);
      print('🔍 Language provider notifier exists: ${languageNotifier != null}');
    } catch (e) {
      print('🔍 Language provider error: $e');
    }

    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      locale: currentLocale,

      // 🔧 FIXED: Custom localization delegates with fallback support
      localizationsDelegates: const [
        AppLocalizations.delegate,
        CustomMaterialLocalizationsDelegate(),
        CustomCupertinoLocalizationsDelegate(),
        GlobalWidgetsLocalizations.delegate,
      ],

      supportedLocales: AppLocalizations.supportedLocales,

      localeResolutionCallback: (locale, supportedLocales) {
        print('\n🔍 === LOCALE RESOLUTION CALLBACK ===');
        print('🔍 Requested locale: $locale');
        print('🔍 Supported locales: $supportedLocales');

        if (locale != null) {
          print('🔍 Checking if ${locale.languageCode} is supported...');
          for (var supportedLocale in supportedLocales) {
            print('🔍 Comparing with: ${supportedLocale.languageCode}');
            if (supportedLocale.languageCode == locale.languageCode) {
              print('✅ Found matching locale: $supportedLocale');
              return supportedLocale;
            }
          }
          print('❌ No matching locale found for ${locale.languageCode}');
        }

        final fallbackLocale = ref.read(currentLocaleProvider);
        print('🔄 Using fallback locale: $fallbackLocale');

        // 🔧 FIXED: Custom delegates handle Material/Cupertino fallbacks automatically
        if (fallbackLocale.languageCode == 'rw') {
          print('✅ Using Kinyarwanda for app, English fallback for Material components');
        }

        return fallbackLocale;
      },

      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.light,

      initialRoute: '/splash',

      routes: {
        '/splash': (context) => const SplashScreen(),
        '/login': (context) => const LoginScreen(),
        '/register': (context) => const RegisterScreen(),
        '/forgot-password': (context) => const ForgotPasswordScreen(),
        '/dashboard': (context) => const RoleDashboard(),

        // Admin routes
        '/admin/dashboard': (context) => const AdminDashboard(),
        '/admin/users': (context) => const UserManagementScreen(),
        '/admin/clients': (context) => const ClientManagementScreen(),
        '/admin/analytics': (context) => const AnalyticsScreen(),
        '/admin/reports': (context) => const ReportsScreen(),
        '/admin/content': (context) => const ContentManagementScreen(),
        '/admin/facilities': (context) =>
        const admin_facilities.HealthFacilitiesScreen(),
        '/admin/settings': (context) => const SystemSettingsScreen(),
        '/admin/files': (context) => const FileManagementScreen(),

        // Health Worker routes
        '/health-worker/main': (context) =>
            HealthWorkerMainScreen(healthWorkerId: 0),

        // Client routes
        '/client/dashboard': (context) => const ClientDashboard(),

        // Common feature routes
        '/profile': (context) => const ProfileScreen(),
        '/settings': (context) => const SettingsScreen(),
        '/advanced-settings': (context) => const AdvancedSettingsScreen(),
        '/notifications': (context) => const NotificationsScreen(),
        '/messages': (context) => const MessagesTab(),
        '/appointments': (context) => const AppointmentsScreen(),
        '/health-records': (context) => const HealthRecordsScreen(),
        '/medications': (context) => const MedicationsScreen(),
        '/contraception': (context) => const ContraceptionScreen(),
        '/menstrual-cycle': (context) => const MenstrualCycleScreen(),
        '/sti-testing': (context) => const StiTestingScreen(),
        '/education': (context) => const EducationScreen(),
        '/support-groups': (context) => const SupportGroupsScreen(),
        '/community-events': (context) => const CommunityEventsScreen(),
        '/feedback': (context) => const FeedbackScreen(),
        '/health-facilities': (context) => const HealthFacilitiesScreen(),

        // Family Planning routes
        '/pregnancy-planning': (context) => const PregnancyPlanningScreen(),
        '/due-date-calculator': (context) => const DueDateCalculatorScreen(),
        '/ovulation-calculator': (context) => const OvulationCalculatorScreen(),
        '/health-checklist': (context) => const HealthChecklistScreen(),
        '/partner-management': (context) => const PartnerManagementScreen(),
        '/partner-decisions': (context) => const PartnerDecisionsScreen(),

        // AI Chat
        '/ai-chat': (context) => const ChatAssistantScreen(),

        // Clinic Finder
        '/clinic-finder': (context) => const ClinicFinderScreen(),
      },

      builder: (context, child) {
        if (child == null) return const SizedBox.shrink();

        // 🔍 DEBUG: Builder context
        print('\n🔍 === MATERIAL APP BUILDER ===');
        final mediaQuery = MediaQuery.of(context);
        print('🔍 MediaQuery data exists: ${mediaQuery != null}');

        // Check if AppLocalizations is working
        try {
          final localizations = AppLocalizations.of(context);
          if (localizations != null) {
            print('✅ AppLocalizations loaded successfully');
            print('🔍 AppLocalizations locale: ${localizations.localeName}');

            // Test a simple translation
            try {
              final testTranslation = localizations.appName;
              print('🔍 Test translation (appName): $testTranslation');
            } catch (e) {
              print('❌ Error getting test translation: $e');
            }
          } else {
            print('❌ AppLocalizations is null');
          }
        } catch (e) {
          print('❌ Error accessing AppLocalizations: $e');
        }

        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaleFactor: 1.0, // ✅ Forces linear scaling
          ),
          child: child,
        );
      },
    );
  }
}